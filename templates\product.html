{% extends 'layout.html' %}

{% block title %}{{ product.name }}{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- مسار التنقل -->
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ url_for('index') }}">الرئيسية</a></li>
                    <li class="breadcrumb-item"><a href="{{ url_for('products') }}">المنتجات</a></li>
                    <li class="breadcrumb-item"><a href="{{ url_for('category', id=product.category_id) }}">{{ product.category.name }}</a></li>
                    <li class="breadcrumb-item active" aria-current="page">{{ product.name }}</li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- تفاصيل المنتج -->
    <div class="row mb-5">
        <!-- صور المنتج -->
        <div class="col-lg-6 mb-4 mb-lg-0">
            <div class="product-images">
                <div class="row">
                    <div class="col-md-2 order-md-1 order-2">
                        <!-- الصور المصغرة -->
                        <div class="product-thumbnails d-flex flex-md-column flex-row mb-md-0 mb-3">
                            {% if product.images %}
                                {% set image_list = product.images.split(',') %}
                                {% for image in image_list %}
                                <div class="thumbnail-item mb-md-2 me-md-0 me-2 {% if loop.index == 1 %}active{% endif %}">
                                    <img src="{{ url_for('static', filename='uploads/' + image) }}" alt="{{ product.name }} - صورة {{ loop.index }}" class="img-thumbnail" data-src="{{ url_for('static', filename='uploads/' + image) }}">
                                </div>
                                {% endfor %}
                            {% else %}
                                <div class="thumbnail-item mb-md-2 me-md-0 me-2 active">
                                    <div class="img-thumbnail bg-light d-flex align-items-center justify-content-center" style="height: 80px;">
                                        <i class="fas fa-image text-secondary"></i>
                                    </div>
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-10 order-md-2 order-1">
                        <!-- الصورة الرئيسية -->
                        <div class="product-main-image mb-3">
                            {% if product.images %}
                                {% set main_image = product.images.split(',')[0] %}
                                <img id="main-product-image" src="{{ url_for('static', filename='uploads/' + main_image) }}" alt="{{ product.name }}" class="img-fluid rounded">
                            {% else %}
                                <div class="bg-light rounded d-flex align-items-center justify-content-center" style="height: 400px;">
                                    <i class="fas fa-image fa-4x text-secondary"></i>
                                </div>
                            {% endif %}
                        </div>
                        <!-- شارات المنتج -->
                        <div class="product-badges mb-3">
                            {% if product.is_new %}
                            <span class="badge bg-success me-2">جديد</span>
                            {% endif %}
                            {% if product.discount_percent > 0 %}
                            <span class="badge bg-danger me-2">خصم {{ product.discount_percent }}%</span>
                            {% endif %}
                            {% if product.stock <= 5 and product.stock > 0 %}
                            <span class="badge bg-warning text-dark">كمية محدودة</span>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- معلومات المنتج -->
        <div class="col-lg-6">
            <div class="product-info">
                <h1 class="product-title mb-2">{{ product.name }}</h1>
                
                <!-- التقييم -->
                <div class="product-rating mb-3">
                    <div class="d-flex align-items-center">
                        <div class="me-2">
                            {% for i in range(5) %}
                                {% if i < product.rating|int %}
                                    <i class="fas fa-star text-warning"></i>
                                {% elif (product.rating - i) >= 0.5 %}
                                    <i class="fas fa-star-half-alt text-warning"></i>
                                {% else %}
                                    <i class="far fa-star text-warning"></i>
                                {% endif %}
                            {% endfor %}
                        </div>
                        <div class="text-muted">
                            <span>{{ product.rating|default('0.0', true) }}</span>
                            <span class="mx-1">|</span>
                            <span>{{ product.reviews|default(0, true) }} تقييم</span>
                            {% if product.sales > 0 %}
                            <span class="mx-1">|</span>
                            <span>{{ product.sales }} مبيعات</span>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <!-- السعر -->
                <div class="product-price mb-3">
                    {% if product.discount_price %}
                    <div class="d-flex align-items-center">
                        <span class="current-price text-danger h3 mb-0">{{ product.discount_price }} ر.س</span>
                        <span class="original-price text-muted text-decoration-line-through ms-2">{{ product.price }} ر.س</span>
                        <span class="discount-badge ms-2 badge bg-danger">خصم {{ product.discount_percent }}%</span>
                    </div>
                    {% else %}
                    <span class="current-price h3 mb-0">{{ product.price }} ر.س</span>
                    {% endif %}
                </div>
                
                <!-- الوصف المختصر -->
                <div class="product-short-description mb-4">
                    <p>{{ product.short_description|default(product.description[:150] ~ '...', true) }}</p>
                </div>
                
                <!-- التوفر -->
                <div class="product-availability mb-4">
                    <div class="d-flex align-items-center">
                        <span class="me-2">التوفر:</span>
                        {% if product.stock > 10 %}
                        <span class="text-success"><i class="fas fa-check-circle me-1"></i> متوفر</span>
                        {% elif product.stock > 0 %}
                        <span class="text-warning"><i class="fas fa-exclamation-circle me-1"></i> متوفر ({{ product.stock }} قطعة فقط)</span>
                        {% else %}
                        <span class="text-danger"><i class="fas fa-times-circle me-1"></i> غير متوفر</span>
                        {% endif %}
                    </div>
                </div>
                
                <!-- الكمية والإضافة للسلة -->
                {% if product.stock > 0 %}
                <div class="product-actions mb-4">
                    <form id="add-to-cart-form" action="{{ url_for('add_to_cart') }}" method="post">
                        <input type="hidden" name="product_id" value="{{ product.id }}">
                        <div class="row">
                            <div class="col-md-4 col-6 mb-3 mb-md-0">
                                <label for="quantity" class="form-label">الكمية</label>
                                <div class="input-group quantity-selector">
                                    <button type="button" class="btn btn-outline-secondary quantity-decrease">
                                        <i class="fas fa-minus"></i>
                                    </button>
                                    <input type="number" class="form-control text-center" id="quantity" name="quantity" value="1" min="1" max="{{ product.stock }}">
                                    <button type="button" class="btn btn-outline-secondary quantity-increase">
                                        <i class="fas fa-plus"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-8 col-12">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-flex gap-2">
                                    <button type="submit" class="btn btn-primary flex-grow-1">
                                        <i class="fas fa-shopping-cart me-2"></i> أضف إلى السلة
                                    </button>
                                    <button type="button" class="btn btn-outline-danger add-to-favorites" data-product-id="{{ product.id }}">
                                        <i class="far fa-heart"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                {% else %}
                <div class="product-actions mb-4">
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i> هذا المنتج غير متوفر حالياً
                    </div>
                    <button type="button" class="btn btn-outline-primary notify-stock">
                        <i class="fas fa-bell me-2"></i> أعلمني عند توفر المنتج
                    </button>
                </div>
                {% endif %}
                
                <!-- معلومات إضافية -->
                <div class="product-meta">
                    <div class="mb-2">
                        <span class="text-muted">رمز المنتج:</span>
                        <span>{{ product.sku|default('SKU-' ~ product.id, true) }}</span>
                    </div>
                    <div class="mb-2">
                        <span class="text-muted">الفئة:</span>
                        <a href="{{ url_for('category', id=product.category_id) }}">{{ product.category.name }}</a>
                    </div>
                    {% if product.tags %}
                    <div class="mb-2">
                        <span class="text-muted">الوسوم:</span>
                        {% for tag in product.tags.split(',') %}
                        <a href="{{ url_for('products', tag=tag.strip()) }}" class="badge bg-light text-dark text-decoration-none me-1">{{ tag.strip() }}</a>
                        {% endfor %}
                    </div>
                    {% endif %}
                </div>
                
                <!-- مشاركة المنتج -->
                <div class="product-share mt-4">
                    <div class="d-flex align-items-center">
                        <span class="me-3">مشاركة:</span>
                        <div class="social-share">
                            <a href="#" class="me-2 text-primary"><i class="fab fa-facebook-f"></i></a>
                            <a href="#" class="me-2 text-info"><i class="fab fa-twitter"></i></a>
                            <a href="#" class="me-2 text-success"><i class="fab fa-whatsapp"></i></a>
                            <a href="#" class="me-2 text-danger"><i class="fab fa-pinterest"></i></a>
                            <a href="#" class="text-secondary"><i class="fas fa-envelope"></i></a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- تفاصيل المنتج والتقييمات -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white">
                    <ul class="nav nav-tabs card-header-tabs" id="productTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="description-tab" data-bs-toggle="tab" data-bs-target="#description" type="button" role="tab" aria-controls="description" aria-selected="true">الوصف</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="specifications-tab" data-bs-toggle="tab" data-bs-target="#specifications" type="button" role="tab" aria-controls="specifications" aria-selected="false">المواصفات</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="reviews-tab" data-bs-toggle="tab" data-bs-target="#reviews" type="button" role="tab" aria-controls="reviews" aria-selected="false">التقييمات <span class="badge bg-secondary ms-1">{{ product.reviews|default(0, true) }}</span></button>
                        </li>
                    </ul>
                </div>
                <div class="card-body p-4">
                    <div class="tab-content" id="productTabsContent">
                        <!-- قسم الوصف -->
                        <div class="tab-pane fade show active" id="description" role="tabpanel" aria-labelledby="description-tab">
                            <div class="product-description">
                                {{ product.description|safe }}
                            </div>
                        </div>
                        
                        <!-- قسم المواصفات -->
                        <div class="tab-pane fade" id="specifications" role="tabpanel" aria-labelledby="specifications-tab">
                            <div class="product-specifications">
                                <div class="table-responsive">
                                    <table class="table table-striped">
                                        <tbody>
                                            {% if product.specifications %}
                                                {% for spec in product.specifications.items() %}
                                                <tr>
                                                    <th style="width: 30%">{{ spec[0] }}</th>
                                                    <td>{{ spec[1] }}</td>
                                                </tr>
                                                {% endfor %}
                                            {% else %}
                                                <tr>
                                                    <th>المادة</th>
                                                    <td>فضة خالصة عيار 925</td>
                                                </tr>
                                                <tr>
                                                    <th>الوزن</th>
                                                    <td>{{ (product.id * 3.5)|round(1, 'floor') }} جرام</td>
                                                </tr>
                                                <tr>
                                                    <th>بلد المنشأ</th>
                                                    <td>المملكة العربية السعودية</td>
                                                </tr>
                                                <tr>
                                                    <th>الضمان</th>
                                                    <td>سنة واحدة</td>
                                                </tr>
                                            {% endif %}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                        
                        <!-- قسم التقييمات -->
                        <div class="tab-pane fade" id="reviews" role="tabpanel" aria-labelledby="reviews-tab">
                            <div class="product-reviews">
                                <!-- ملخص التقييمات -->
                                <div class="row mb-4">
                                    <div class="col-md-4 mb-4 mb-md-0">
                                        <div class="review-summary text-center">
                                            <div class="display-4 fw-bold mb-2">{{ product.rating|default('0.0', true) }}</div>
                                            <div class="mb-2">
                                                {% for i in range(5) %}
                                                    {% if i < product.rating|int %}
                                                        <i class="fas fa-star text-warning"></i>
                                                    {% elif (product.rating - i) >= 0.5 %}
                                                        <i class="fas fa-star-half-alt text-warning"></i>
                                                    {% else %}
                                                        <i class="far fa-star text-warning"></i>
                                                    {% endif %}
                                                {% endfor %}
                                            </div>
                                            <div class="text-muted">{{ product.reviews|default(0, true) }} تقييم</div>
                                        </div>
                                    </div>
                                    <div class="col-md-8">
                                        <div class="review-bars">
                                            {% for i in range(5, 0, -1) %}
                                            <div class="d-flex align-items-center mb-2">
                                                <div class="me-2" style="width: 60px;">
                                                    <div class="d-flex align-items-center">
                                                        <span class="me-1">{{ i }}</span>
                                                        <i class="fas fa-star text-warning"></i>
                                                    </div>
                                                </div>
                                                <div class="progress flex-grow-1" style="height: 10px;">
                                                    {% set percentage = (product.rating_counts[i]|default(0) / product.reviews|default(1) * 100)|round if product.reviews else 0 %}
                                                    <div class="progress-bar bg-warning" role="progressbar" style="width: {{ percentage }}%" aria-valuenow="{{ percentage }}" aria-valuemin="0" aria-valuemax="100"></div>
                                                </div>
                                                <div class="ms-2" style="width: 40px;">
                                                    {{ percentage }}%
                                                </div>
                                            </div>
                                            {% endfor %}
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- قائمة التقييمات -->
                                <div class="reviews-list">
                                    <h4 class="mb-3">التقييمات</h4>
                                    
                                    {% if product.reviews_list %}
                                        {% for review in product.reviews_list %}
                                        <div class="review-item mb-4 pb-4 border-bottom">
                                            <div class="d-flex">
                                                <div class="review-avatar me-3">
                                                    <div class="avatar-placeholder bg-primary text-white rounded-circle d-flex align-items-center justify-content-center">
                                                        {{ review.user_name[:1] }}
                                                    </div>
                                                </div>
                                                <div class="review-content">
                                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                                        <h5 class="mb-0">{{ review.user_name }}</h5>
                                                        <small class="text-muted">{{ review.date }}</small>
                                                    </div>
                                                    <div class="review-rating mb-2">
                                                        {% for i in range(5) %}
                                                            {% if i < review.rating %}
                                                                <i class="fas fa-star text-warning"></i>
                                                            {% else %}
                                                                <i class="far fa-star text-warning"></i>
                                                            {% endif %}
                                                        {% endfor %}
                                                    </div>
                                                    <p class="review-text mb-2">{{ review.comment }}</p>
                                                    {% if review.images %}
                                                    <div class="review-images d-flex flex-wrap gap-2 mb-3">
                                                        {% for image in review.images %}
                                                        <div class="review-image-item">
                                                            <img src="{{ url_for('static', filename='uploads/' + image) }}" alt="صورة التقييم" class="img-thumbnail" style="width: 80px; height: 80px; object-fit: cover;">
                                                        </div>
                                                        {% endfor %}
                                                    </div>
                                                    {% endif %}
                                                    {% if review.reply %}
                                                    <div class="review-reply bg-light p-3 rounded mt-3">
                                                        <div class="d-flex">
                                                            <div class="review-reply-avatar me-2">
                                                                <div class="avatar-placeholder bg-secondary text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 30px; height: 30px; font-size: 12px;">
                                                                    A
                                                                </div>
                                                            </div>
                                                            <div class="review-reply-content">
                                                                <div class="d-flex justify-content-between align-items-center mb-2">
                                                                    <h6 class="mb-0">فضيات الملك <span class="badge bg-primary ms-1">المدير</span></h6>
                                                                    <small class="text-muted">{{ review.reply_date }}</small>
                                                                </div>
                                                                <p class="review-reply-text mb-0">{{ review.reply }}</p>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    {% endif %}
                                                </div>
                                            </div>
                                        </div>
                                        {% endfor %}
                                    {% else %}
                                        <div class="text-center py-4">
                                            <div class="mb-3">
                                                <i class="far fa-comment-dots fa-3x text-muted"></i>
                                            </div>
                                            <h5>لا توجد تقييمات بعد</h5>
                                            <p class="text-muted">كن أول من يقيم هذا المنتج</p>
                                        </div>
                                    {% endif %}
                                </div>
                                
                                <!-- نموذج إضافة تقييم -->
                                <div class="add-review mt-4">
                                    <h4 class="mb-3">أضف تقييمك</h4>
                                    {% if current_user.is_authenticated %}
                                    <form id="review-form" action="{{ url_for('add_review', product_id=product.id) }}" method="post" enctype="multipart/form-data">
                                        <div class="mb-3">
                                            <label class="form-label">التقييم</label>
                                            <div class="rating-stars mb-2">
                                                <i class="far fa-star rating-star" data-rating="1"></i>
                                                <i class="far fa-star rating-star" data-rating="2"></i>
                                                <i class="far fa-star rating-star" data-rating="3"></i>
                                                <i class="far fa-star rating-star" data-rating="4"></i>
                                                <i class="far fa-star rating-star" data-rating="5"></i>
                                            </div>
                                            <input type="hidden" name="rating" id="rating-value" value="0">
                                        </div>
                                        <div class="mb-3">
                                            <label for="review-comment" class="form-label">التعليق</label>
                                            <textarea class="form-control" id="review-comment" name="comment" rows="4" required></textarea>
                                        </div>
                                        <div class="mb-3">
                                            <label for="review-images" class="form-label">إضافة صور (اختياري)</label>
                                            <input class="form-control" type="file" id="review-images" name="images" multiple accept="image/*">
                                            <div class="form-text">يمكنك إضافة حتى 3 صور</div>
                                        </div>
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-paper-plane me-2"></i> إرسال التقييم
                                        </button>
                                    </form>
                                    {% else %}
                                    <div class="alert alert-info">
                                        <i class="fas fa-info-circle me-2"></i> يجب عليك <a href="{{ url_for('login') }}" class="alert-link">تسجيل الدخول</a> لإضافة تقييم
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- المنتجات ذات الصلة -->
    <div class="related-products mt-5">
        <h3 class="section-title mb-4">منتجات ذات صلة</h3>
        <div class="row g-4">
            {% for related_product in related_products[:4] %}
            <div class="col-lg-3 col-md-6">
                <div class="product-card card h-100 border-0 shadow-sm">
                    <div class="position-relative">
                        <a href="{{ url_for('product', id=related_product.id) }}">
                            <div class="product-img-container" style="height: 200px;">
                                {% if related_product.images %}
                                <img src="{{ url_for('static', filename='uploads/' + related_product.images.split(',')[0]) }}" alt="{{ related_product.name }}" class="card-img-top object-fit-cover h-100">
                                {% else %}
                                <div class="bg-light w-100 h-100 d-flex align-items-center justify-content-center">
                                    <i class="fas fa-image fa-3x text-secondary"></i>
                                </div>
                                {% endif %}
                            </div>
                        </a>
                        {% if related_product.discount_percent > 0 %}
                        <span class="badge bg-danger position-absolute top-0 end-0 m-2">خصم {{ related_product.discount_percent }}%</span>
                        {% endif %}
                    </div>
                    <div class="card-body p-3">
                        <h5 class="card-title mb-1">
                            <a href="{{ url_for('product', id=related_product.id) }}" class="text-decoration-none text-dark">{{ related_product.name }}</a>
                        </h5>
                        <div class="product-price mb-2">
                            {% if related_product.discount_price %}
                            <span class="text-danger fw-bold">{{ related_product.discount_price }} ر.س</span>
                            <small class="text-muted text-decoration-line-through">{{ related_product.price }} ر.س</small>
                            {% else %}
                            <span class="fw-bold">{{ related_product.price }} ر.س</span>
                            {% endif %}
                        </div>
                        <div class="d-grid">
                            {% if related_product.stock > 0 %}
                            <button class="btn btn-sm btn-outline-primary add-to-cart" data-product-id="{{ related_product.id }}">
                                <i class="fas fa-shopping-cart me-1"></i> أضف للسلة
                            </button>
                            {% else %}
                            <button class="btn btn-sm btn-outline-secondary" disabled>
                                <i class="fas fa-times-circle me-1"></i> نفذت الكمية
                            </button>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
</div>

<style>
.product-thumbnails {
    max-height: 400px;
    overflow-y: auto;
}

.thumbnail-item {
    cursor: pointer;
    border: 2px solid transparent;
    transition: all 0.2s ease;
}

.thumbnail-item.active {
    border-color: #0d6efd;
}

.thumbnail-item img {
    width: 80px;
    height: 80px;
    object-fit: cover;
}

.avatar-placeholder {
    width: 50px;
    height: 50px;
    font-size: 20px;
}

.rating-stars {
    font-size: 24px;
    cursor: pointer;
}

.rating-stars .fa-star {
    color: #ccc;
    transition: all 0.2s ease;
}

.rating-stars .fas.fa-star {
    color: #ffc107;
}

.section-title {
    position: relative;
    padding-bottom: 10px;
    margin-bottom: 20px;
}

.section-title::after {
    content: "";
    position: absolute;
    bottom: 0;
    right: 0;
    width: 50px;
    height: 3px;
    background-color: #0d6efd;
}

@media (max-width: 767.98px) {
    .product-thumbnails {
        max-height: none;
        overflow-x: auto;
        white-space: nowrap;
        margin-bottom: 15px;
    }
    
    .thumbnail-item img {
        width: 60px;
        height: 60px;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // تبديل الصورة الرئيسية عند النقر على الصور المصغرة
    const thumbnails = document.querySelectorAll('.thumbnail-item');
    const mainImage = document.getElementById('main-product-image');
    
    thumbnails.forEach(thumbnail => {
        thumbnail.addEventListener('click', function() {
            // إزالة الفئة النشطة من جميع الصور المصغرة
            thumbnails.forEach(item => item.classList.remove('active'));
            
            // إضافة الفئة النشطة للصورة المصغرة المحددة
            this.classList.add('active');
            
            // تحديث الصورة الرئيسية
            const imgElement = this.querySelector('img');
            if (imgElement && imgElement.dataset.src) {
                mainImage.src = imgElement.dataset.src;
            }
        });
    });
    
    // التعامل مع زيادة ونقصان الكمية
    const quantityInput = document.getElementById('quantity');
    const decreaseBtn = document.querySelector('.quantity-decrease');
    const increaseBtn = document.querySelector('.quantity-increase');
    
    if (quantityInput && decreaseBtn && increaseBtn) {
        decreaseBtn.addEventListener('click', function() {
            let value = parseInt(quantityInput.value);
            if (value > 1) {
                quantityInput.value = value - 1;
            }
        });
        
        increaseBtn.addEventListener('click', function() {
            let value = parseInt(quantityInput.value);
            let max = parseInt(quantityInput.getAttribute('max'));
            if (value < max) {
                quantityInput.value = value + 1;
            }
        });
    }
    
    // التعامل مع تقييم النجوم
    const ratingStars = document.querySelectorAll('.rating-star');
    const ratingValue = document.getElementById('rating-value');
    
    if (ratingStars.length > 0 && ratingValue) {
        ratingStars.forEach(star => {
            star.addEventListener('mouseover', function() {
                const rating = parseInt(this.dataset.rating);
                highlightStars(rating);
            });
            
            star.addEventListener('mouseout', function() {
                const currentRating = parseInt(ratingValue.value);
                highlightStars(currentRating);
            });
            
            star.addEventListener('click', function() {
                const rating = parseInt(this.dataset.rating);
                ratingValue.value = rating;
                highlightStars(rating);
            });
        });
        
        function highlightStars(rating) {
            ratingStars.forEach(star => {
                const starRating = parseInt(star.dataset.rating);
                if (starRating <= rating) {
                    star.classList.remove('far');
                    star.classList.add('fas');
                } else {
                    star.classList.remove('fas');
                    star.classList.add('far');
                }
            });
        }
    }
});
</script>
{% endblock %}