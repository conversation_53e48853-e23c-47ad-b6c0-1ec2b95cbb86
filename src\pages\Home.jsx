import React from 'react';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import { FaRocket, FaPalette, FaBullhorn, FaCode, FaStar, FaPhone, FaWhatsapp } from 'react-icons/fa';
import { useStudio } from '../context/StudioContext';

const Home = () => {
  const { packages, contactInfo } = useStudio();
  const popularPackages = packages.filter(pkg => pkg.popular).slice(0, 3);

  const services = [
    {
      icon: <FaPalette />,
      title: 'التصميم الجرافيكي',
      description: 'تصميمات إبداعية تعكس هوية علامتك التجارية'
    },
    {
      icon: <FaBullhorn />,
      title: 'الإعلانات الرقمية',
      description: 'حملات إعلانية فعالة على جميع المنصات'
    },
    {
      icon: <FaCode />,
      title: 'تطوير المواقع',
      description: 'مواقع إلكترونية احترافية وسريعة الاستجابة'
    },
    {
      icon: <FaRocket />,
      title: 'التسويق الرقمي',
      description: 'استراتيجيات تسويقية متطورة لنمو أعمالك'
    }
  ];

  const stats = [
    { number: '500+', label: 'عميل راضي' },
    { number: '1000+', label: 'مشروع مكتمل' },
    { number: '5+', label: 'سنوات خبرة' },
    { number: '24/7', label: 'دعم فني' }
  ];

  return (
    <div className="home-page">
      {/* Hero Section */}
      <section className="hero-section">
        <div className="hero-content">
          <motion.div 
            className="hero-text"
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            <h1>
              مرحباً بك في <span className="brand-name">Y-Studio</span>
            </h1>
            <p className="hero-subtitle">
              استوديو الإعلانات والتصميم الرائد في مصر
            </p>
            <p className="hero-description">
              نحن نقدم حلولاً إبداعية متكاملة لتطوير هوية علامتك التجارية وزيادة مبيعاتك
              من خلال التصميم الاحترافي والإعلانات الفعالة
            </p>
            <div className="hero-buttons">
              <Link to="/packages" className="btn btn-primary">
                استكشف باقاتنا
              </Link>
              <a 
                href={`https://wa.me/${contactInfo.phone}`} 
                className="btn btn-secondary"
                target="_blank"
                rel="noopener noreferrer"
              >
                <FaWhatsapp /> تواصل معنا
              </a>
            </div>
          </motion.div>
          
          <motion.div 
            className="hero-image"
            initial={{ opacity: 0, x: 50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
          >
            <div className="hero-graphic">
              <div className="floating-card card-1">
                <FaPalette />
                <span>تصميم</span>
              </div>
              <div className="floating-card card-2">
                <FaBullhorn />
                <span>إعلانات</span>
              </div>
              <div className="floating-card card-3">
                <FaCode />
                <span>تطوير</span>
              </div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Services Section */}
      <section className="services-section">
        <div className="container">
          <motion.div 
            className="section-header"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <h2>خدماتنا المتميزة</h2>
            <p>نقدم مجموعة شاملة من الخدمات الإبداعية لتلبية جميع احتياجاتك</p>
          </motion.div>
          
          <div className="services-grid">
            {services.map((service, index) => (
              <motion.div 
                key={index}
                className="service-card"
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                whileHover={{ y: -10 }}
              >
                <div className="service-icon">
                  {service.icon}
                </div>
                <h3>{service.title}</h3>
                <p>{service.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Popular Packages Section */}
      <section className="popular-packages-section">
        <div className="container">
          <motion.div 
            className="section-header"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <h2>الباقات الأكثر طلباً</h2>
            <p>اختر من بين باقاتنا المميزة التي تناسب احتياجاتك وميزانيتك</p>
          </motion.div>
          
          <div className="packages-grid">
            {popularPackages.map((pkg, index) => (
              <motion.div 
                key={pkg.id}
                className="package-card popular"
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                whileHover={{ y: -10 }}
              >
                <div className="package-badge">
                  <FaStar /> الأكثر طلباً
                </div>
                <h3>{pkg.name}</h3>
                <p className="package-description">{pkg.description}</p>
                <div className="package-price">
                  <span className="price">{pkg.price}</span>
                  <span className="currency">جنيه</span>
                </div>
                <ul className="package-features">
                  {pkg.features.slice(0, 3).map((feature, idx) => (
                    <li key={idx}>{feature}</li>
                  ))}
                </ul>
                <Link to={`/packages/${pkg.id}`} className="btn btn-primary">
                  اطلب الآن
                </Link>
              </motion.div>
            ))}
          </div>
          
          <div className="packages-cta">
            <Link to="/packages" className="btn btn-outline">
              عرض جميع الباقات
            </Link>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="stats-section">
        <div className="container">
          <div className="stats-grid">
            {stats.map((stat, index) => (
              <motion.div 
                key={index}
                className="stat-item"
                initial={{ opacity: 0, scale: 0.8 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <div className="stat-number">{stat.number}</div>
                <div className="stat-label">{stat.label}</div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="cta-section">
        <div className="container">
          <motion.div 
            className="cta-content"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <h2>هل أنت مستعد لبدء مشروعك؟</h2>
            <p>تواصل معنا اليوم واحصل على استشارة مجانية لمشروعك</p>
            <div className="cta-buttons">
              <a 
                href={`tel:${contactInfo.phone}`} 
                className="btn btn-primary"
              >
                <FaPhone /> اتصل بنا الآن
              </a>
              <a 
                href={`https://wa.me/${contactInfo.phone}`} 
                className="btn btn-whatsapp"
                target="_blank"
                rel="noopener noreferrer"
              >
                <FaWhatsapp /> واتساب
              </a>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  );
};

export default Home;
