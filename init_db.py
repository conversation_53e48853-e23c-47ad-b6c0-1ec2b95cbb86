# init_db.py - تهيئة قاعدة البيانات وإضافة بيانات افتراضية

from app import app, db
from models import User, Category, Product
from werkzeug.security import generate_password_hash
import os

# إنشاء مجلد التحميلات إذا لم يكن موجوداً
uploads_dir = os.path.join(app.static_folder, 'uploads')
if not os.path.exists(uploads_dir):
    os.makedirs(uploads_dir)

# تهيئة قاعدة البيانات
with app.app_context():
    # حذف جميع الجداول وإعادة إنشائها
    db.drop_all()
    db.create_all()
    
    print("تم إنشاء قاعدة البيانات بنجاح!")
    
    # إضافة مستخدم المسؤول
    admin = User(
        username="admin",
        password=generate_password_hash("admin123"),
        email="<EMAIL>",
        is_admin=True
    )
    db.session.add(admin)
    
    # إضافة مستخدم عادي
    user = User(
        username="user",
        password=generate_password_hash("user123"),
        email="<EMAIL>",
        is_admin=False
    )
    db.session.add(user)
    
    # إضافة فئات المنتجات
    categories = [
        Category(name="خواتم", description="خواتم فضة بتصاميم مختلفة"),
        Category(name="أساور", description="أساور فضة بتصاميم مختلفة"),
        Category(name="قلائد", description="قلائد فضة بتصاميم مختلفة"),
        Category(name="أقراط", description="أقراط فضة بتصاميم مختلفة"),
        Category(name="خلاخيل", description="خلاخيل فضة بتصاميم مختلفة")
    ]
    
    for category in categories:
        db.session.add(category)
    
    # حفظ التغييرات لإنشاء معرفات الفئات
    db.session.commit()
    
    # الحصول على الفئات من قاعدة البيانات
    rings_category = Category.query.filter_by(name="خواتم").first()
    bracelets_category = Category.query.filter_by(name="أساور").first()
    necklaces_category = Category.query.filter_by(name="قلائد").first()
    earrings_category = Category.query.filter_by(name="أقراط").first()
    
    # إضافة منتجات افتراضية
    products = [
        Product(
            name="خاتم فضة مرصع",
            description="خاتم فضة عيار 925 مرصع بأحجار الزركون",
            price=150.00,
            stock=10,
            category_id=rings_category.id,
            image="ring1.jpg"
        ),
        Product(
            name="خاتم فضة بتصميم كلاسيكي",
            description="خاتم فضة عيار 925 بتصميم كلاسيكي أنيق",
            price=120.00,
            stock=15,
            category_id=rings_category.id,
            image="ring2.jpg"
        ),
        Product(
            name="سوار فضة مجدول",
            description="سوار فضة عيار 925 بتصميم مجدول فاخر",
            price=200.00,
            stock=8,
            category_id=bracelets_category.id,
            image="bracelet1.jpg"
        ),
        Product(
            name="سوار فضة مع حجر فيروز",
            description="سوار فضة عيار 925 مزين بحجر فيروز طبيعي",
            price=250.00,
            stock=5,
            category_id=bracelets_category.id,
            image="bracelet2.jpg"
        ),
        Product(
            name="قلادة فضة مع قلب",
            description="قلادة فضة عيار 925 بتصميم قلب أنيق",
            price=180.00,
            stock=12,
            category_id=necklaces_category.id,
            image="necklace1.jpg"
        ),
        Product(
            name="قلادة فضة طويلة",
            description="قلادة فضة عيار 925 بتصميم طويل وأنيق",
            price=220.00,
            stock=7,
            category_id=necklaces_category.id,
            image="necklace2.jpg"
        ),
        Product(
            name="أقراط فضة متدلية",
            description="أقراط فضة عيار 925 بتصميم متدلي أنيق",
            price=130.00,
            stock=20,
            category_id=earrings_category.id,
            image="earring1.jpg"
        ),
        Product(
            name="أقراط فضة دائرية",
            description="أقراط فضة عيار 925 بتصميم دائري كلاسيكي",
            price=100.00,
            stock=25,
            category_id=earrings_category.id,
            image="earring2.jpg"
        )
    ]
    
    for product in products:
        db.session.add(product)
    
    # حفظ جميع التغييرات
    db.session.commit()
    
    print(f"تم إضافة {len(products)} منتج بنجاح!")
    print(f"تم إضافة {len(categories)} فئة بنجاح!")
    print(f"تم إضافة المستخدمين بنجاح!")
    print("\nبيانات تسجيل الدخول:")
    print("المسؤول: username=admin, password=admin123")
    print("المستخدم: username=user, password=user123")

print("\nتم تهيئة قاعدة البيانات بنجاح!")