# تعليمات تثبيت وتشغيل فضيات الملك

هذا الملف يحتوي على تعليمات تفصيلية لتثبيت وتشغيل تطبيق فضيات الملك.

## متطلبات النظام

- Python 3.6 أو أحدث
- pip (مدير حزم Python)
- وصول للإنترنت لتنزيل الحزم المطلوبة

## خطوات التثبيت

### 1. تنزيل المشروع

إذا كنت تستخدم Git:

```bash
git clone https://github.com/yourusername/silver-jewelry-store.git
cd silver-jewelry-store
```

أو قم بتنزيل المشروع كملف ZIP وفك ضغطه.

### 2. إنشاء بيئة افتراضية

ننصح بإنشاء بيئة افتراضية لعزل تثبيت الحزم الخاصة بالمشروع:

```bash
# في Windows
python -m venv venv

# تفعيل البيئة الافتراضية في Windows
venv\Scripts\activate

# في macOS/Linux
python3 -m venv venv

# تفعيل البيئة الافتراضية في macOS/Linux
source venv/bin/activate
```

### 3. تثبيت الحزم المطلوبة

```bash
pip install -r requirements.txt
```

### 4. تهيئة ملف الإعدادات البيئية

قم بتعديل ملف `.env` حسب احتياجاتك. الإعدادات الافتراضية مناسبة للتطوير المحلي.

### 5. تهيئة قاعدة البيانات

```bash
python init_db.py
```

هذا سيقوم بإنشاء قاعدة البيانات وإضافة بيانات افتراضية للاختبار.

### 6. تشغيل التطبيق

```bash
python run.py
```

سيتم تشغيل التطبيق على العنوان المحلي: `http://127.0.0.1:5000`

## بيانات تسجيل الدخول الافتراضية

- **المسؤول**:
  - اسم المستخدم: `admin`
  - كلمة المرور: `admin123`

- **مستخدم عادي**:
  - اسم المستخدم: `user`
  - كلمة المرور: `user123`

## هيكل المجلدات

- `static/`: يحتوي على الملفات الثابتة (CSS، JavaScript، الصور)
  - `uploads/`: مجلد لتخزين صور المنتجات المرفوعة
- `templates/`: يحتوي على قوالب HTML
- `app.py`: ملف التطبيق الرئيسي
- `models.py`: نماذج قاعدة البيانات
- `config.py`: إعدادات التطبيق

## استكشاف الأخطاء وإصلاحها

### مشكلة: خطأ في تفعيل البيئة الافتراضية

- تأكد من تثبيت Python بشكل صحيح
- تأكد من إنشاء البيئة الافتراضية في المجلد الصحيح
- جرب إعادة إنشاء البيئة الافتراضية

### مشكلة: خطأ في تثبيت الحزم

- تأكد من تفعيل البيئة الافتراضية
- تأكد من وجود اتصال بالإنترنت
- جرب تحديث pip: `pip install --upgrade pip`

### مشكلة: خطأ في تشغيل التطبيق

- تأكد من تهيئة قاعدة البيانات
- تأكد من تثبيت جميع الحزم المطلوبة
- تحقق من وجود جميع الملفات في المجلدات الصحيحة

## للإنتاج

للنشر في بيئة الإنتاج، يجب اتخاذ خطوات إضافية:

1. تغيير `SECRET_KEY` في ملف `.env`
2. ضبط `DEBUG=False` في ملف `.env`
3. استخدام خادم WSGI مثل Gunicorn أو uWSGI
4. استخدام قاعدة بيانات أكثر قوة مثل PostgreSQL
5. إعداد خادم وكيل عكسي مثل Nginx

## المساعدة

إذا واجهت أي مشاكل أو كان لديك أسئلة، يرجى فتح مشكلة في مستودع GitHub أو التواصل مع المطور.