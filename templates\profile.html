{% extends 'layout.html' %}

{% block title %}الملف الشخصي{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-md-3 mb-4">
            <div class="card shadow-sm">
                <div class="card-body text-center py-4">
                    <div class="avatar-circle mb-3 mx-auto">
                        <span class="avatar-initials">{{ current_user.username[0] | upper }}</span>
                    </div>
                    <h5 class="card-title">{{ current_user.username }}</h5>
                    <p class="card-text text-muted">{{ current_user.email }}</p>
                    <div class="d-grid gap-2 mt-3">
                        <a href="{{ url_for('dashboard') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-tachometer-alt me-2"></i> لوحة التحكم
                        </a>
                    </div>
                </div>
                <div class="list-group list-group-flush">
                    <a href="{{ url_for('profile') }}" class="list-group-item list-group-item-action active">
                        <i class="fas fa-user me-2"></i> الملف الشخصي
                    </a>
                    <a href="{{ url_for('orders') }}" class="list-group-item list-group-item-action">
                        <i class="fas fa-shopping-bag me-2"></i> طلباتي
                    </a>
                    <a href="{{ url_for('cart') }}" class="list-group-item list-group-item-action">
                        <i class="fas fa-shopping-cart me-2"></i> سلة التسوق
                    </a>
                    <a href="{{ url_for('favorites') }}" class="list-group-item list-group-item-action">
                        <i class="fas fa-heart me-2"></i> المفضلة
                    </a>
                    <a href="{{ url_for('addresses') }}" class="list-group-item list-group-item-action">
                        <i class="fas fa-map-marker-alt me-2"></i> العناوين
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Main Content -->
        <div class="col-md-9">
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-white py-3">
                    <h5 class="mb-0"><i class="fas fa-user-edit me-2"></i> تعديل الملف الشخصي</h5>
                </div>
                <div class="card-body">
                    {% if success_message %}
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle me-2"></i> {{ success_message }}
                    </div>
                    {% endif %}
                    
                    <form method="POST" action="{{ url_for('profile') }}">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="username" class="form-label">اسم المستخدم</label>
                                <input type="text" class="form-control" id="username" name="username" value="{{ current_user.username }}" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="email" class="form-label">البريد الإلكتروني</label>
                                <input type="email" class="form-control" id="email" name="email" value="{{ current_user.email }}" required>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="first_name" class="form-label">الاسم الأول</label>
                                <input type="text" class="form-control" id="first_name" name="first_name" value="{{ current_user.first_name or '' }}">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="last_name" class="form-label">الاسم الأخير</label>
                                <input type="text" class="form-control" id="last_name" name="last_name" value="{{ current_user.last_name or '' }}">
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="phone" class="form-label">رقم الهاتف</label>
                                <input type="tel" class="form-control" id="phone" name="phone" value="{{ current_user.phone or '' }}">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="birthdate" class="form-label">تاريخ الميلاد</label>
                                <input type="date" class="form-control" id="birthdate" name="birthdate" value="{{ current_user.birthdate or '' }}">
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="bio" class="form-label">نبذة شخصية</label>
                            <textarea class="form-control" id="bio" name="bio" rows="3">{{ current_user.bio or '' }}</textarea>
                        </div>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i> حفظ التغييرات
                        </button>
                    </form>
                </div>
            </div>
            
            <div class="card shadow-sm">
                <div class="card-header bg-white py-3">
                    <h5 class="mb-0"><i class="fas fa-lock me-2"></i> تغيير كلمة المرور</h5>
                </div>
                <div class="card-body">
                    {% if password_message %}
                    <div class="alert alert-{{ 'success' if password_success else 'danger' }}">
                        <i class="fas fa-{{ 'check-circle' if password_success else 'exclamation-circle' }} me-2"></i> {{ password_message }}
                    </div>
                    {% endif %}
                    
                    <form method="POST" action="{{ url_for('change_password') }}">
                        <div class="mb-3">
                            <label for="current_password" class="form-label">كلمة المرور الحالية</label>
                            <input type="password" class="form-control" id="current_password" name="current_password" required>
                        </div>
                        <div class="mb-3">
                            <label for="new_password" class="form-label">كلمة المرور الجديدة</label>
                            <input type="password" class="form-control" id="new_password" name="new_password" required>
                            <div class="form-text">يجب أن تحتوي كلمة المرور على 8 أحرف على الأقل.</div>
                        </div>
                        <div class="mb-3">
                            <label for="confirm_password" class="form-label">تأكيد كلمة المرور الجديدة</label>
                            <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                        </div>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-key me-2"></i> تغيير كلمة المرور
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.avatar-circle {
    width: 80px;
    height: 80px;
    background-color: #6c757d;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
}

.avatar-initials {
    color: white;
    font-size: 32px;
    font-weight: bold;
    text-transform: uppercase;
}
</style>
{% endblock %}