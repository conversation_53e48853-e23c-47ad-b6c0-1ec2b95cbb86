{% extends 'layout.html' %}

{% block title %}نتائج البحث{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ url_for('index') }}">الرئيسية</a></li>
                    <li class="breadcrumb-item active" aria-current="page">نتائج البحث</li>
                </ol>
            </nav>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-8">
            <h2 class="mb-0">نتائج البحث عن "{{ query }}"</h2>
            <p class="text-muted">تم العثور على {{ products|length }} منتج</p>
        </div>
        <div class="col-md-4">
            <form action="{{ url_for('search') }}" method="GET" class="d-flex">
                <input type="text" name="q" class="form-control" placeholder="ابحث عن منتجات..." value="{{ query }}" required>
                <button type="submit" class="btn btn-primary ms-2">
                    <i class="fas fa-search"></i>
                </button>
            </form>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-white">
                    <h5 class="mb-0">تصفية النتائج</h5>
                </div>
                <div class="card-body">
                    <form action="{{ url_for('search') }}" method="GET" id="filterForm">
                        <input type="hidden" name="q" value="{{ query }}">
                        
                        <div class="mb-3">
                            <label class="form-label fw-bold">الفئات</label>
                            {% for category in categories %}
                            <div class="form-check">
                                <input class="form-check-input filter-checkbox" type="checkbox" name="category" value="{{ category.id }}" id="category{{ category.id }}" {% if category.id|string in selected_categories %}checked{% endif %}>
                                <label class="form-check-label" for="category{{ category.id }}">
                                    {{ category.name }}
                                </label>
                            </div>
                            {% endfor %}
                        </div>
                        
                        <div class="mb-3">
                            <label for="priceRange" class="form-label fw-bold">نطاق السعر</label>
                            <div class="d-flex align-items-center">
                                <input type="number" class="form-control form-control-sm" name="min_price" placeholder="من" value="{{ min_price }}">
                                <span class="mx-2">-</span>
                                <input type="number" class="form-control form-control-sm" name="max_price" placeholder="إلى" value="{{ max_price }}">
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label fw-bold">الترتيب حسب</label>
                            <select class="form-select" name="sort" id="sortSelect">
                                <option value="relevance" {% if sort == 'relevance' %}selected{% endif %}>الأكثر صلة</option>
                                <option value="price_asc" {% if sort == 'price_asc' %}selected{% endif %}>السعر: من الأقل إلى الأعلى</option>
                                <option value="price_desc" {% if sort == 'price_desc' %}selected{% endif %}>السعر: من الأعلى إلى الأقل</option>
                                <option value="newest" {% if sort == 'newest' %}selected{% endif %}>الأحدث</option>
                                <option value="name_asc" {% if sort == 'name_asc' %}selected{% endif %}>الاسم: أ-ي</option>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label fw-bold">الحالة</label>
                            <div class="form-check">
                                <input class="form-check-input filter-checkbox" type="checkbox" name="in_stock" value="1" id="inStock" {% if in_stock %}checked{% endif %}>
                                <label class="form-check-label" for="inStock">
                                    متوفر في المخزون
                                </label>
                            </div>
                        </div>
                        
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-filter me-1"></i> تطبيق الفلتر
                            </button>
                            <a href="{{ url_for('search', q=query) }}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-1"></i> إعادة ضبط
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="col-md-9">
            {% if products %}
            <div class="row">
                {% for product in products %}
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="card product-card h-100">
                        <div class="position-relative">
                            <img src="{{ url_for('static', filename='uploads/' + product.image) }}" class="card-img-top" alt="{{ product.name }}">
                            {% if current_user.is_authenticated %}
                            <form method="POST" action="{% if product.id in favorite_ids %}{{ url_for('remove_favorite', product_id=product.id) }}{% else %}{{ url_for('add_favorite', product_id=product.id) }}{% endif %}" class="position-absolute top-0 end-0 m-2">
                                <button type="submit" class="btn btn-sm {% if product.id in favorite_ids %}btn-danger{% else %}btn-outline-danger{% endif %} rounded-circle">
                                    <i class="fas fa-heart"></i>
                                </button>
                            </form>
                            {% endif %}
                            {% if product.stock <= 0 %}
                            <div class="out-of-stock-badge">نفذت الكمية</div>
                            {% endif %}
                        </div>
                        <div class="card-body d-flex flex-column">
                            <h5 class="card-title">{{ product.name }}</h5>
                            <p class="card-text text-muted mb-2">{{ product.category.name }}</p>
                            <p class="card-text description-preview">{{ product.description|truncate(60) }}</p>
                            <div class="d-flex justify-content-between align-items-center mt-auto">
                                <span class="price">{{ product.price }} ريال</span>
                                <div>
                                    <a href="{{ url_for('product_details', product_id=product.id) }}" class="btn btn-sm btn-outline-secondary me-1">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    {% if product.stock > 0 %}
                                    <form method="POST" action="{{ url_for('add_to_cart', product_id=product.id) }}" class="d-inline">
                                        <input type="hidden" name="quantity" value="1">
                                        <button type="submit" class="btn btn-sm btn-primary">
                                            <i class="fas fa-shopping-cart"></i>
                                        </button>
                                    </form>
                                    {% else %}
                                    <button class="btn btn-sm btn-secondary" disabled>
                                        <i class="fas fa-shopping-cart"></i>
                                    </button>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
            
            {% if pagination.pages > 1 %}
            <nav aria-label="Page navigation" class="mt-4">
                <ul class="pagination justify-content-center">
                    {% if pagination.has_prev %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('search', q=query, page=pagination.prev_num, category=request.args.getlist('category'), min_price=min_price, max_price=max_price, sort=sort, in_stock=in_stock) }}" aria-label="Previous">
                            <span aria-hidden="true">&laquo;</span>
                        </a>
                    </li>
                    {% else %}
                    <li class="page-item disabled">
                        <a class="page-link" href="#" aria-label="Previous">
                            <span aria-hidden="true">&laquo;</span>
                        </a>
                    </li>
                    {% endif %}
                    
                    {% for page in pagination.iter_pages(left_edge=1, right_edge=1, left_current=2, right_current=2) %}
                    {% if page %}
                    <li class="page-item {% if page == pagination.page %}active{% endif %}">
                        <a class="page-link" href="{{ url_for('search', q=query, page=page, category=request.args.getlist('category'), min_price=min_price, max_price=max_price, sort=sort, in_stock=in_stock) }}">{{ page }}</a>
                    </li>
                    {% else %}
                    <li class="page-item disabled">
                        <a class="page-link" href="#">...</a>
                    </li>
                    {% endif %}
                    {% endfor %}
                    
                    {% if pagination.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('search', q=query, page=pagination.next_num, category=request.args.getlist('category'), min_price=min_price, max_price=max_price, sort=sort, in_stock=in_stock) }}" aria-label="Next">
                            <span aria-hidden="true">&raquo;</span>
                        </a>
                    </li>
                    {% else %}
                    <li class="page-item disabled">
                        <a class="page-link" href="#" aria-label="Next">
                            <span aria-hidden="true">&raquo;</span>
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
            {% endif %}
            
            {% else %}
            <div class="card shadow-sm">
                <div class="card-body text-center py-5">
                    <i class="fas fa-search fa-3x text-muted mb-3"></i>
                    <h4>لم يتم العثور على نتائج</h4>
                    <p class="text-muted">لم نتمكن من العثور على أي منتجات تطابق "{{ query }}"</p>
                    <div class="mt-3">
                        <a href="{{ url_for('index') }}" class="btn btn-primary">
                            <i class="fas fa-home me-1"></i> العودة إلى الصفحة الرئيسية
                        </a>
                    </div>
                    <div class="mt-4">
                        <h5>اقتراحات:</h5>
                        <ul class="list-unstyled">
                            <li>تأكد من كتابة جميع الكلمات بشكل صحيح.</li>
                            <li>جرب كلمات مفتاحية مختلفة.</li>
                            <li>جرب كلمات مفتاحية أكثر عمومية.</li>
                            <li>قلل عدد الفلاتر المطبقة.</li>
                        </ul>
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<style>
.product-card .card-img-top {
    height: 200px;
    object-fit: cover;
}

.price {
    font-weight: bold;
    color: #dc3545;
}

.description-preview {
    font-size: 0.9rem;
    color: #6c757d;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.out-of-stock-badge {
    position: absolute;
    top: 10px;
    left: 10px;
    background-color: rgba(108, 117, 125, 0.85);
    color: white;
    padding: 5px 10px;
    font-size: 0.8rem;
    border-radius: 4px;
}
</style>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Auto-submit form when sort option changes
        document.getElementById('sortSelect').addEventListener('change', function() {
            document.getElementById('filterForm').submit();
        });
    });
</script>
{% endblock %}