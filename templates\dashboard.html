{% extends 'layout.html' %}

{% block title %}لوحة التحكم{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex align-items-center mb-4">
            <i class="fas fa-tachometer-alt fa-2x text-primary me-3"></i>
            <h1 class="mb-0">لوحة التحكم</h1>
        </div>
        <hr class="mb-4">
    </div>
</div>

{% if session.get('is_admin') %}
<!-- لوحة تحكم المسؤول -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card shadow-sm">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0"><i class="fas fa-chart-pie me-2"></i>إحصائيات النظام</h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-md-3 mb-3">
                        <div class="p-3 border rounded bg-light shadow-sm">
                            <div class="d-flex align-items-center justify-content-center mb-2">
                                <i class="fas fa-box-open text-primary fa-2x"></i>
                            </div>
                            <h3 class="text-primary">{{ products|default(0, true)|length }}</h3>
                            <p class="mb-0">إجمالي المنتجات</p>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="p-3 border rounded bg-light shadow-sm">
                            <div class="d-flex align-items-center justify-content-center mb-2">
                                <i class="fas fa-tags text-success fa-2x"></i>
                            </div>
                            <h3 class="text-success">{{ categories|default(0, true)|length }}</h3>
                            <p class="mb-0">إجمالي الفئات</p>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="p-3 border rounded bg-light shadow-sm">
                            <div class="d-flex align-items-center justify-content-center mb-2">
                                <i class="fas fa-shopping-cart text-info fa-2x"></i>
                            </div>
                            <h3 class="text-info">{{ orders|default(0, true)|length }}</h3>
                            <p class="mb-0">إجمالي الطلبات</p>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="p-3 border rounded bg-light shadow-sm">
                            <div class="d-flex align-items-center justify-content-center mb-2">
                                <i class="fas fa-users text-warning fa-2x"></i>
                            </div>
                            <h3 class="text-warning">{{ users|default(0, true)|length }}</h3>
                            <p class="mb-0">إجمالي المستخدمين</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- إدارة المنتجات -->
    <div class="col-md-6 mb-4">
        <div class="card shadow-sm h-100">
            <div class="card-header bg-light">
                <h5 class="mb-0"><i class="fas fa-box-open me-2"></i>إدارة المنتجات</h5>
            </div>
            <div class="card-body">
                <p>يمكنك إدارة منتجات المتجر من هنا.</p>
                <div class="d-grid gap-2">
                    <a href="{{ url_for('products') }}" class="btn btn-outline-primary"><i class="fas fa-list me-1"></i> عرض المنتجات</a>
                    <a href="{{ url_for('add_product') }}" class="btn btn-primary"><i class="fas fa-plus-circle me-1"></i> إضافة منتج جديد</a>
                </div>
            </div>
        </div>
    </div>
    
    <!-- إدارة الفئات -->
    <div class="col-md-6 mb-4">
        <div class="card shadow-sm h-100">
            <div class="card-header bg-light">
                <h5 class="mb-0"><i class="fas fa-tags me-2"></i>إدارة الفئات</h5>
            </div>
            <div class="card-body">
                <p>يمكنك إدارة فئات المنتجات من هنا.</p>
                <div class="d-grid gap-2">
                    <a href="{{ url_for('categories') }}" class="btn btn-outline-primary"><i class="fas fa-list me-1"></i> عرض الفئات</a>
                    <a href="{{ url_for('add_category') }}" class="btn btn-primary"><i class="fas fa-plus-circle me-1"></i> إضافة فئة جديدة</a>
                </div>
            </div>
        </div>
    </div>
    
    <!-- إدارة الطلبات -->
    <div class="col-md-6 mb-4">
        <div class="card shadow-sm h-100">
            <div class="card-header bg-light">
                <h5 class="mb-0"><i class="fas fa-shopping-cart me-2"></i>إدارة الطلبات</h5>
            </div>
            <div class="card-body">
                <p>يمكنك متابعة وإدارة طلبات العملاء من هنا.</p>
                <div class="d-grid gap-2">
                    <a href="{{ url_for('orders') }}" class="btn btn-primary">عرض الطلبات</a>
                </div>
            </div>
        </div>
    </div>
    
    <!-- إدارة المستخدمين -->
    <div class="col-md-6 mb-4">
        <div class="card shadow-sm h-100">
            <div class="card-header bg-light">
                <h5 class="mb-0"><i class="fas fa-users me-2"></i>إدارة المستخدمين</h5>
            </div>
            <div class="card-body">
                <p>يمكنك إدارة حسابات المستخدمين والمسؤولين من هنا.</p>
                <div class="d-grid gap-2">
                    <a href="{{ url_for('users') }}" class="btn btn-primary">عرض المستخدمين</a>
                </div>
            </div>
        </div>
    </div>
    
    <!-- تقارير المبيعات -->
    <div class="col-md-6 mb-4">
        <div class="card shadow-sm h-100">
            <div class="card-header bg-light">
                <h5 class="mb-0"><i class="fas fa-chart-bar me-2"></i>تقارير المبيعات</h5>
            </div>
            <div class="card-body">
                <p>يمكنك عرض تقارير المبيعات والإحصائيات من هنا.</p>
                <div class="d-grid gap-2">
                    <a href="{{ url_for('sales_reports') }}" class="btn btn-primary"><i class="fas fa-chart-line me-1"></i> عرض التقارير</a>
                </div>
            </div>
        </div>
    </div>
    
    <!-- إعدادات النظام -->
    <div class="col-md-6 mb-4">
        <div class="card shadow-sm h-100">
            <div class="card-header bg-light">
                <h5 class="mb-0"><i class="fas fa-cogs me-2"></i>إعدادات النظام</h5>
            </div>
            <div class="card-body">
                <p>يمكنك تخصيص إعدادات المتجر والنظام من هنا.</p>
                <div class="d-grid gap-2">
                    <a href="{{ url_for('settings') }}" class="btn btn-primary"><i class="fas fa-sliders-h me-1"></i> عرض الإعدادات</a>
                </div>
            </div>
        </div>
    </div>
</div>

{% else %}
<!-- لوحة تحكم المستخدم العادي -->
<div class="row">
    <!-- معلومات الحساب -->
    <div class="col-md-6 mb-4">
        <div class="card shadow-sm h-100">
            <div class="card-header bg-light">
                <h5 class="mb-0"><i class="fas fa-user-circle me-2"></i>معلومات الحساب</h5>
            </div>
            <div class="card-body">
                <p>مرحباً بك في حسابك الشخصي.</p>
                <div class="d-grid gap-2">
                    <a href="#" class="btn btn-outline-primary"><i class="fas fa-user-edit me-1"></i> تعديل الملف الشخصي</a>
                    <a href="#" class="btn btn-outline-secondary"><i class="fas fa-key me-1"></i> تغيير كلمة المرور</a>
                </div>
            </div>
        </div>
    </div>
    
    <!-- طلباتي -->
    <div class="col-md-6 mb-4">
        <div class="card shadow-sm h-100">
            <div class="card-header bg-light">
                <h5 class="mb-0"><i class="fas fa-shopping-bag me-2"></i>طلباتي</h5>
            </div>
            <div class="card-body">
                <p>يمكنك متابعة طلباتك السابقة من هنا.</p>
                <div class="d-grid gap-2">
                    <a href="#" class="btn btn-primary"><i class="fas fa-list me-1"></i> عرض طلباتي</a>
                </div>
            </div>
        </div>
    </div>
    
    <!-- سلة التسوق -->
    <div class="col-md-6 mb-4">
        <div class="card shadow-sm h-100">
            <div class="card-header bg-light">
                <h5 class="mb-0"><i class="fas fa-shopping-cart me-2"></i>سلة التسوق</h5>
            </div>
            <div class="card-body">
                <p>يمكنك متابعة سلة التسوق الخاصة بك من هنا.</p>
                <div class="d-grid gap-2">
                    <a href="{{ url_for('cart') }}" class="btn btn-primary"><i class="fas fa-shopping-basket me-1"></i> عرض سلة التسوق</a>
                </div>
            </div>
        </div>
    </div>
    
    <!-- المفضلة -->
    <div class="col-md-6 mb-4">
        <div class="card shadow-sm h-100">
            <div class="card-header bg-light">
                <h5 class="mb-0"><i class="fas fa-heart me-2"></i>المنتجات المفضلة</h5>
            </div>
            <div class="card-body">
                <p>يمكنك عرض المنتجات المفضلة لديك من هنا.</p>
                <div class="d-grid gap-2">
                    <a href="{{ url_for('favorites') }}" class="btn btn-primary"><i class="fas fa-star me-1"></i> عرض المفضلة</a>
                </div>
            </div>
        </div>
    </div>
    
    <!-- عناوين الشحن -->
    <div class="col-md-6 mb-4">
        <div class="card shadow-sm h-100">
            <div class="card-header bg-light">
                <h5 class="mb-0"><i class="fas fa-map-marker-alt me-2"></i>عناوين الشحن</h5>
            </div>
            <div class="card-body">
                <p>يمكنك إدارة عناوين الشحن الخاصة بك من هنا.</p>
                <div class="d-grid gap-2">
                    <a href="{{ url_for('addresses') }}" class="btn btn-primary"><i class="fas fa-address-book me-1"></i> إدارة العناوين</a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}