{% extends 'layout.html' %}

{% block title %}إضافة منتج جديد{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card shadow">
            <div class="card-header bg-primary text-white">
                <h3 class="mb-0">إضافة منتج جديد</h3>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('add_product') }}" enctype="multipart/form-data">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="name" class="form-label">اسم المنتج</label>
                            <input type="text" class="form-control" id="name" name="name" required>
                        </div>
                        <div class="col-md-6">
                            <label for="price" class="form-label">السعر (ريال)</label>
                            <input type="number" class="form-control" id="price" name="price" step="0.01" min="0" required>
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="category" class="form-label">الفئة</label>
                            <select class="form-select" id="category" name="category" required>
                                <option value="" selected disabled>اختر الفئة</option>
                                {% for category in categories %}
                                <option value="{{ category.id }}">{{ category.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="stock" class="form-label">الكمية المتوفرة</label>
                            <input type="number" class="form-control" id="stock" name="stock" min="0" required>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">وصف المنتج</label>
                        <textarea class="form-control" id="description" name="description" rows="4" required></textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label for="image" class="form-label">صورة المنتج</label>
                        <input type="file" class="form-control" id="image" name="image" accept="image/*">
                        <div class="form-text">الصيغ المدعومة: JPG, PNG, GIF. الحجم الأقصى: 5MB</div>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('products') }}" class="btn btn-secondary">إلغاء</a>
                        <button type="submit" class="btn btn-primary">إضافة المنتج</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // يمكن إضافة التحقق من صحة النموذج باستخدام جافاسكربت هنا
    document.getElementById('image').addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            // التحقق من حجم الملف (5MB كحد أقصى)
            if (file.size > 5 * 1024 * 1024) {
                alert('حجم الملف كبير جدًا. الحد الأقصى هو 5 ميجابايت.');
                e.target.value = '';
            }
        }
    });
</script>
{% endblock %}