// Silver Jewelry Store - Main JavaScript File

document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Initialize popovers
    var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });

    // Flash messages auto-close
    setTimeout(function() {
        var alerts = document.querySelectorAll('.alert-dismissible.auto-close');
        alerts.forEach(function(alert) {
            var bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
        });
    }, 5000);

    // Product image preview
    const productImageInput = document.getElementById('image');
    const imagePreview = document.getElementById('imagePreview');
    
    if (productImageInput && imagePreview) {
        productImageInput.addEventListener('change', function() {
            const file = this.files[0];
            if (file) {
                const reader = new FileReader();
                
                reader.onload = function(e) {
                    imagePreview.src = e.target.result;
                    imagePreview.style.display = 'block';
                }
                
                reader.readAsDataURL(file);
                
                // Check file size
                const maxSize = 5 * 1024 * 1024; // 5MB
                if (file.size > maxSize) {
                    alert('حجم الصورة كبير جداً. الحد الأقصى هو 5 ميجابايت.');
                    this.value = '';
                    imagePreview.src = '';
                    imagePreview.style.display = 'none';
                }
            }
        });
    }

    // Quantity selector in product details
    const quantityInput = document.querySelector('.quantity-selector input');
    const incrementBtn = document.querySelector('.quantity-selector .increment');
    const decrementBtn = document.querySelector('.quantity-selector .decrement');
    
    if (quantityInput && incrementBtn && decrementBtn) {
        incrementBtn.addEventListener('click', function() {
            let currentValue = parseInt(quantityInput.value);
            const max = parseInt(quantityInput.getAttribute('max')) || 99;
            if (currentValue < max) {
                quantityInput.value = currentValue + 1;
            }
        });
        
        decrementBtn.addEventListener('click', function() {
            let currentValue = parseInt(quantityInput.value);
            if (currentValue > 1) {
                quantityInput.value = currentValue - 1;
            }
        });
        
        quantityInput.addEventListener('change', function() {
            let currentValue = parseInt(this.value);
            const max = parseInt(this.getAttribute('max')) || 99;
            
            if (isNaN(currentValue) || currentValue < 1) {
                this.value = 1;
            } else if (currentValue > max) {
                this.value = max;
            }
        });
    }

    // Cart quantity update
    const cartQuantityInputs = document.querySelectorAll('.cart-quantity-input');
    
    cartQuantityInputs.forEach(function(input) {
        input.addEventListener('change', function() {
            const itemId = this.getAttribute('data-item-id');
            const quantity = parseInt(this.value);
            const max = parseInt(this.getAttribute('max')) || 99;
            
            if (isNaN(quantity) || quantity < 1) {
                this.value = 1;
                updateCartItem(itemId, 1);
            } else if (quantity > max) {
                this.value = max;
                updateCartItem(itemId, max);
            } else {
                updateCartItem(itemId, quantity);
            }
        });
    });

    function updateCartItem(itemId, quantity) {
        fetch('/cart/update', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify({
                item_id: itemId,
                quantity: quantity
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Update item subtotal
                const subtotalElement = document.querySelector(`.cart-item[data-item-id="${itemId}"] .item-subtotal`);
                if (subtotalElement) {
                    subtotalElement.textContent = data.item_subtotal + ' ريال';
                }
                
                // Update cart total
                const cartTotalElement = document.querySelector('.cart-total');
                if (cartTotalElement) {
                    cartTotalElement.textContent = data.cart_total + ' ريال';
                }
            } else {
                alert(data.message || 'حدث خطأ أثناء تحديث السلة');
            }
        })
        .catch(error => {
            console.error('Error updating cart:', error);
        });
    }

    // Payment method selection in checkout
    const paymentMethods = document.querySelectorAll('input[name="payment_method"]');
    const paymentDetails = document.querySelectorAll('.payment-details');
    
    if (paymentMethods.length > 0 && paymentDetails.length > 0) {
        paymentMethods.forEach(function(method) {
            method.addEventListener('change', function() {
                // Hide all payment details sections
                paymentDetails.forEach(function(details) {
                    details.style.display = 'none';
                });
                
                // Show the selected payment method details
                const selectedMethod = this.value;
                const selectedDetails = document.querySelector(`.payment-details[data-method="${selectedMethod}"]`);
                if (selectedDetails) {
                    selectedDetails.style.display = 'block';
                }
            });
        });
        
        // Trigger change event on the checked payment method
        const checkedMethod = document.querySelector('input[name="payment_method"]:checked');
        if (checkedMethod) {
            checkedMethod.dispatchEvent(new Event('change'));
        }
    }

    // Confirmation dialogs
    const confirmForms = document.querySelectorAll('.confirm-form');
    
    confirmForms.forEach(function(form) {
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const message = this.getAttribute('data-confirm-message') || 'هل أنت متأكد من إتمام هذا الإجراء؟';
            
            if (confirm(message)) {
                this.submit();
            }
        });
    });

    // Search functionality
    const searchForm = document.getElementById('searchForm');
    const searchInput = document.getElementById('searchInput');
    
    if (searchForm && searchInput) {
        searchForm.addEventListener('submit', function(e) {
            if (searchInput.value.trim() === '') {
                e.preventDefault();
                searchInput.focus();
            }
        });
    }
});