{% extends 'layout.html' %}

{% block title %}تعديل المنتج{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card shadow">
            <div class="card-header bg-warning text-dark">
                <h3 class="mb-0">تعديل المنتج: {{ product.name }}</h3>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('edit_product', product_id=product.id) }}" enctype="multipart/form-data">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="name" class="form-label">اسم المنتج</label>
                            <input type="text" class="form-control" id="name" name="name" value="{{ product.name }}" required>
                        </div>
                        <div class="col-md-6">
                            <label for="price" class="form-label">السعر (ريال)</label>
                            <input type="number" class="form-control" id="price" name="price" step="0.01" min="0" value="{{ product.price }}" required>
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="category" class="form-label">الفئة</label>
                            <select class="form-select" id="category" name="category" required>
                                {% for category in categories %}
                                <option value="{{ category.id }}" {% if product.category == category.id %}selected{% endif %}>{{ category.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="stock" class="form-label">الكمية المتوفرة</label>
                            <input type="number" class="form-control" id="stock" name="stock" min="0" value="{{ product.stock }}" required>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">وصف المنتج</label>
                        <textarea class="form-control" id="description" name="description" rows="4" required>{{ product.description }}</textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label for="image" class="form-label">صورة المنتج</label>
                        {% if product.image %}
                        <div class="mb-2">
                            <img src="{{ url_for('static', filename='uploads/' + product.image) }}" alt="{{ product.name }}" class="img-thumbnail" style="max-height: 150px;">
                            <p class="form-text">الصورة الحالية: {{ product.image }}</p>
                        </div>
                        {% endif %}
                        <input type="file" class="form-control" id="image" name="image" accept="image/*">
                        <div class="form-text">الصيغ المدعومة: JPG, PNG, GIF. الحجم الأقصى: 5MB. اترك هذا الحقل فارغًا للاحتفاظ بالصورة الحالية.</div>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('products') }}" class="btn btn-secondary">إلغاء</a>
                        <button type="submit" class="btn btn-warning">تحديث المنتج</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // يمكن إضافة التحقق من صحة النموذج باستخدام جافاسكربت هنا
    document.getElementById('image').addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            // التحقق من حجم الملف (5MB كحد أقصى)
            if (file.size > 5 * 1024 * 1024) {
                alert('حجم الملف كبير جدًا. الحد الأقصى هو 5 ميجابايت.');
                e.target.value = '';
            }
        }
    });
</script>
{% endblock %}