{% extends 'layout.html' %}

{% block title %}المفضلة{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-md-3 mb-4">
            <div class="card shadow-sm">
                <div class="card-body text-center py-4">
                    <div class="avatar-circle mb-3 mx-auto">
                        <span class="avatar-initials">{{ current_user.username[0] | upper }}</span>
                    </div>
                    <h5 class="card-title">{{ current_user.username }}</h5>
                    <p class="card-text text-muted">{{ current_user.email }}</p>
                    <div class="d-grid gap-2 mt-3">
                        <a href="{{ url_for('dashboard') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-tachometer-alt me-2"></i> لوحة التحكم
                        </a>
                    </div>
                </div>
                <div class="list-group list-group-flush">
                    <a href="{{ url_for('profile') }}" class="list-group-item list-group-item-action">
                        <i class="fas fa-user me-2"></i> الملف الشخصي
                    </a>
                    <a href="{{ url_for('orders') }}" class="list-group-item list-group-item-action">
                        <i class="fas fa-shopping-bag me-2"></i> طلباتي
                    </a>
                    <a href="{{ url_for('cart') }}" class="list-group-item list-group-item-action">
                        <i class="fas fa-shopping-cart me-2"></i> سلة التسوق
                    </a>
                    <a href="{{ url_for('favorites') }}" class="list-group-item list-group-item-action active">
                        <i class="fas fa-heart me-2"></i> المفضلة
                    </a>
                    <a href="{{ url_for('addresses') }}" class="list-group-item list-group-item-action">
                        <i class="fas fa-map-marker-alt me-2"></i> العناوين
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Main Content -->
        <div class="col-md-9">
            <div class="card shadow-sm">
                <div class="card-header bg-white py-3">
                    <h5 class="mb-0"><i class="fas fa-heart me-2"></i> المنتجات المفضلة</h5>
                </div>
                <div class="card-body">
                    {% if favorites %}
                    <div class="row">
                        {% for favorite in favorites %}
                        <div class="col-md-4 col-sm-6 mb-4">
                            <div class="card product-card h-100">
                                <div class="position-relative">
                                    <img src="{{ url_for('static', filename='uploads/' + favorite.product.image) }}" class="card-img-top" alt="{{ favorite.product.name }}">
                                    <form method="POST" action="{{ url_for('remove_favorite', product_id=favorite.product.id) }}" class="position-absolute top-0 end-0 m-2">
                                        <button type="submit" class="btn btn-sm btn-danger rounded-circle">
                                            <i class="fas fa-heart"></i>
                                        </button>
                                    </form>
                                </div>
                                <div class="card-body d-flex flex-column">
                                    <h5 class="card-title">{{ favorite.product.name }}</h5>
                                    <p class="card-text text-muted mb-2">{{ favorite.product.category.name }}</p>
                                    <div class="d-flex justify-content-between align-items-center mt-auto">
                                        <span class="price">{{ favorite.product.price }} ريال</span>
                                        <div>
                                            <a href="{{ url_for('product_details', product_id=favorite.product.id) }}" class="btn btn-sm btn-outline-secondary me-1">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <form method="POST" action="{{ url_for('add_to_cart', product_id=favorite.product.id) }}" class="d-inline">
                                                <input type="hidden" name="quantity" value="1">
                                                <button type="submit" class="btn btn-sm btn-primary">
                                                    <i class="fas fa-shopping-cart"></i>
                                                </button>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    {% else %}
                    <div class="text-center py-5">
                        <i class="far fa-heart fa-3x text-muted mb-3"></i>
                        <h5>لا توجد منتجات في المفضلة</h5>
                        <p class="text-muted">لم تقم بإضافة أي منتجات إلى المفضلة بعد.</p>
                        <a href="{{ url_for('index') }}" class="btn btn-primary mt-2">
                            <i class="fas fa-shopping-bag me-1"></i> تصفح المنتجات
                        </a>
                    </div>
                    {% endif %}
                </div>
                {% if favorites %}
                <div class="card-footer bg-white py-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <span>{{ favorites|length }} منتج في المفضلة</span>
                        <div>
                            <a href="{{ url_for('index') }}" class="btn btn-outline-secondary me-2">
                                <i class="fas fa-shopping-bag me-1"></i> متابعة التسوق
                            </a>
                            <form method="POST" action="{{ url_for('add_all_to_cart') }}" class="d-inline">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-shopping-cart me-1"></i> إضافة الكل إلى السلة
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<style>
.avatar-circle {
    width: 80px;
    height: 80px;
    background-color: #6c757d;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
}

.avatar-initials {
    color: white;
    font-size: 32px;
    font-weight: bold;
    text-transform: uppercase;
}

.product-card .card-img-top {
    height: 180px;
    object-fit: cover;
}

.price {
    font-weight: bold;
    color: #dc3545;
}
</style>
{% endblock %}