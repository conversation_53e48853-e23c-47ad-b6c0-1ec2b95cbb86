import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { FaStar, FaCheck, FaShoppingCart, FaFilter, FaSearch } from 'react-icons/fa';
import { useStudio } from '../context/StudioContext';
import { useAuth } from '../context/AuthContext';

const Packages = () => {
  const { packages } = useStudio();
  const { isAuthenticated } = useAuth();
  const navigate = useNavigate();
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState('popular');

  // فلترة الباقات
  const filteredPackages = packages.filter(pkg => {
    const matchesCategory = selectedCategory === 'all' || pkg.category === selectedCategory;
    const matchesSearch = pkg.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         pkg.description.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesCategory && matchesSearch;
  });

  // ترتيب الباقات
  const sortedPackages = [...filteredPackages].sort((a, b) => {
    switch (sortBy) {
      case 'price-low':
        return a.price - b.price;
      case 'price-high':
        return b.price - a.price;
      case 'popular':
        return b.popular - a.popular;
      default:
        return 0;
    }
  });

  const categories = [
    { value: 'all', label: 'جميع الباقات' },
    { value: 'تصميم', label: 'التصميم' },
    { value: 'إعلانات', label: 'الإعلانات' },
    { value: 'مواقع', label: 'المواقع' }
  ];

  const handleOrderPackage = (packageId) => {
    if (!isAuthenticated) {
      navigate('/login');
      return;
    }
    navigate(`/order/${packageId}`);
  };

  return (
    <div className="packages-page">
      <div className="container">
        {/* Page Header */}
        <motion.div 
          className="page-header"
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <h1>باقات Y-Studio</h1>
          <p>اختر الباقة التي تناسب احتياجاتك وميزانيتك</p>
        </motion.div>

        {/* Filters and Search */}
        <motion.div 
          className="packages-filters"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          <div className="filters-row">
            {/* Search */}
            <div className="search-box">
              <FaSearch className="search-icon" />
              <input
                type="text"
                placeholder="ابحث عن باقة..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>

            {/* Category Filter */}
            <div className="filter-group">
              <FaFilter className="filter-icon" />
              <select 
                value={selectedCategory} 
                onChange={(e) => setSelectedCategory(e.target.value)}
              >
                {categories.map(category => (
                  <option key={category.value} value={category.value}>
                    {category.label}
                  </option>
                ))}
              </select>
            </div>

            {/* Sort */}
            <div className="filter-group">
              <select 
                value={sortBy} 
                onChange={(e) => setSortBy(e.target.value)}
              >
                <option value="popular">الأكثر طلباً</option>
                <option value="price-low">السعر: من الأقل للأعلى</option>
                <option value="price-high">السعر: من الأعلى للأقل</option>
              </select>
            </div>
          </div>
        </motion.div>

        {/* Packages Grid */}
        <div className="packages-grid">
          {sortedPackages.map((pkg, index) => (
            <motion.div 
              key={pkg.id}
              className={`package-card ${pkg.popular ? 'popular' : ''}`}
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              whileHover={{ y: -10, scale: 1.02 }}
            >
              {pkg.popular && (
                <div className="package-badge">
                  <FaStar /> الأكثر طلباً
                </div>
              )}

              <div className="package-header">
                <div className="package-category">{pkg.category}</div>
                <h3 className="package-name">{pkg.name}</h3>
                <p className="package-description">{pkg.description}</p>
              </div>

              <div className="package-price">
                <span className="price-amount">{pkg.price}</span>
                <span className="price-currency">جنيه</span>
              </div>

              <div className="package-features">
                <h4>ما تحصل عليه:</h4>
                <ul>
                  {pkg.features.map((feature, idx) => (
                    <li key={idx}>
                      <FaCheck className="check-icon" />
                      {feature}
                    </li>
                  ))}
                </ul>
              </div>

              <div className="package-actions">
                <motion.button 
                  className="btn btn-primary package-order-btn"
                  onClick={() => handleOrderPackage(pkg.id)}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <FaShoppingCart />
                  اطلب الآن
                </motion.button>
              </div>
            </motion.div>
          ))}
        </div>

        {/* No Results */}
        {sortedPackages.length === 0 && (
          <motion.div 
            className="no-results"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5 }}
          >
            <h3>لا توجد باقات تطابق البحث</h3>
            <p>جرب تغيير معايير البحث أو الفلترة</p>
          </motion.div>
        )}

        {/* Custom Package CTA */}
        <motion.div 
          className="custom-package-cta"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
        >
          <div className="cta-content">
            <h3>لم تجد ما تبحث عنه؟</h3>
            <p>تواصل معنا لإنشاء باقة مخصصة تناسب احتياجاتك تماماً</p>
            <button 
              className="btn btn-outline"
              onClick={() => navigate('/contact')}
            >
              طلب باقة مخصصة
            </button>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default Packages;
