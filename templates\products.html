{% extends 'layout.html' %}

{% block title %}إدارة المنتجات{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>إدارة المنتجات</h1>
    <a href="{{ url_for('add_product') }}" class="btn btn-primary">
        <i class="fas fa-plus me-1"></i> إضافة منتج جديد
    </a>
</div>

<div class="card shadow-sm">
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover">
                <thead class="table-light">
                    <tr>
                        <th>#</th>
                        <th>الصورة</th>
                        <th>الاسم</th>
                        <th>السعر</th>
                        <th>الفئة</th>
                        <th>المخزون</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for product in products %}
                    <tr>
                        <td>{{ product.id }}</td>
                        <td>
                            {% if product.image %}
                            <img src="{{ url_for('static', filename='uploads/' + product.image) }}" alt="{{ product.name }}" width="50" height="50" class="img-thumbnail">
                            {% else %}
                            <img src="{{ url_for('static', filename='img/no-image.jpg') }}" alt="صورة غير متوفرة" width="50" height="50" class="img-thumbnail">
                            {% endif %}
                        </td>
                        <td>{{ product.name }}</td>
                        <td>{{ product.price }} ريال</td>
                        <td>{{ product.category }}</td>
                        <td>
                            {% if product.stock > 10 %}
                            <span class="badge bg-success">{{ product.stock }}</span>
                            {% elif product.stock > 0 %}
                            <span class="badge bg-warning">{{ product.stock }}</span>
                            {% else %}
                            <span class="badge bg-danger">نفذت الكمية</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group" role="group">
                                <a href="{{ url_for('product_details', product_id=product.id) }}" class="btn btn-sm btn-info text-white">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{{ url_for('edit_product', product_id=product.id) }}" class="btn btn-sm btn-warning text-white">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <button type="button" class="btn btn-sm btn-danger" data-bs-toggle="modal" data-bs-target="#deleteModal{{ product.id }}">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                            
                            <!-- Modal for Delete Confirmation -->
                            <div class="modal fade" id="deleteModal{{ product.id }}" tabindex="-1" aria-labelledby="deleteModalLabel{{ product.id }}" aria-hidden="true">
                                <div class="modal-dialog">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <h5 class="modal-title" id="deleteModalLabel{{ product.id }}">تأكيد الحذف</h5>
                                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                        </div>
                                        <div class="modal-body">
                                            هل أنت متأكد من حذف المنتج "{{ product.name }}"؟
                                        </div>
                                        <div class="modal-footer">
                                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                                            <form action="{{ url_for('delete_product', product_id=product.id) }}" method="post">
                                                <button type="submit" class="btn btn-danger">حذف</button>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </td>
                    </tr>
                    {% else %}
                    <tr>
                        <td colspan="7" class="text-center">لا توجد منتجات متاحة حالياً</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}