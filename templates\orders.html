{% extends 'layout.html' %}

{% block title %}إدارة الطلبات{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2><i class="fas fa-shopping-bag me-2"></i> إدارة الطلبات</h2>
        <div>
            <form class="d-flex" method="GET" action="{{ url_for('orders') }}">
                <select name="status" class="form-select me-2">
                    <option value="">جميع الحالات</option>
                    <option value="pending" {% if request.args.get('status') == 'pending' %}selected{% endif %}>قيد الانتظار</option>
                    <option value="processing" {% if request.args.get('status') == 'processing' %}selected{% endif %}>قيد المعالجة</option>
                    <option value="shipped" {% if request.args.get('status') == 'shipped' %}selected{% endif %}>تم الشحن</option>
                    <option value="delivered" {% if request.args.get('status') == 'delivered' %}selected{% endif %}>تم التسليم</option>
                    <option value="cancelled" {% if request.args.get('status') == 'cancelled' %}selected{% endif %}>ملغي</option>
                </select>
                <button type="submit" class="btn btn-outline-primary">تصفية</button>
            </form>
        </div>
    </div>

    {% if orders %}
    <div class="card shadow-sm">
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>رقم الطلب</th>
                            <th>العميل</th>
                            <th>تاريخ الطلب</th>
                            <th>المبلغ الإجمالي</th>
                            <th>الحالة</th>
                            <th>طريقة الدفع</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for order in orders %}
                        <tr>
                            <td>#{{ order.id }}</td>
                            <td>{{ order.customer_name }}</td>
                            <td>{{ order.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                            <td>{{ order.total_amount }} ريال</td>
                            <td>
                                {% if order.status == 'pending' %}
                                <span class="badge bg-warning">قيد الانتظار</span>
                                {% elif order.status == 'processing' %}
                                <span class="badge bg-info">قيد المعالجة</span>
                                {% elif order.status == 'shipped' %}
                                <span class="badge bg-primary">تم الشحن</span>
                                {% elif order.status == 'delivered' %}
                                <span class="badge bg-success">تم التسليم</span>
                                {% elif order.status == 'cancelled' %}
                                <span class="badge bg-danger">ملغي</span>
                                {% else %}
                                <span class="badge bg-secondary">{{ order.status }}</span>
                                {% endif %}
                            </td>
                            <td>{{ order.payment_method }}</td>
                            <td>
                                <div class="btn-group">
                                    <a href="{{ url_for('order_details', order_id=order.id) }}" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <button type="button" class="btn btn-sm btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                                        <i class="fas fa-cog"></i>
                                    </button>
                                    <ul class="dropdown-menu dropdown-menu-end">
                                        <li>
                                            <form action="{{ url_for('update_order_status', order_id=order.id) }}" method="POST">
                                                <input type="hidden" name="status" value="processing">
                                                <button type="submit" class="dropdown-item">تحديث إلى قيد المعالجة</button>
                                            </form>
                                        </li>
                                        <li>
                                            <form action="{{ url_for('update_order_status', order_id=order.id) }}" method="POST">
                                                <input type="hidden" name="status" value="shipped">
                                                <button type="submit" class="dropdown-item">تحديث إلى تم الشحن</button>
                                            </form>
                                        </li>
                                        <li>
                                            <form action="{{ url_for('update_order_status', order_id=order.id) }}" method="POST">
                                                <input type="hidden" name="status" value="delivered">
                                                <button type="submit" class="dropdown-item">تحديث إلى تم التسليم</button>
                                            </form>
                                        </li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li>
                                            <form action="{{ url_for('update_order_status', order_id=order.id) }}" method="POST">
                                                <input type="hidden" name="status" value="cancelled">
                                                <button type="submit" class="dropdown-item text-danger">إلغاء الطلب</button>
                                            </form>
                                        </li>
                                    </ul>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    {% else %}
    <div class="alert alert-info">
        <i class="fas fa-info-circle me-2"></i> لا توجد طلبات متاحة حالياً.
    </div>
    {% endif %}
</div>
{% endblock %}