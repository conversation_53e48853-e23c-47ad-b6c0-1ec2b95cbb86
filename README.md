# فضيات الملك - Silver Jewelry Store

تطبيق ويب مبني بلغة Python وإطار Flask لإدارة متجر مجوهرات فضية مع نظام تسجيل دخول وإدارة للمنتجات والطلبات.

## المميزات

- **نظام تسجيل الدخول**: تسجيل دخول للمستخدمين والمسؤولين
- **إدارة المنتجات**: إضافة، تعديل، حذف وعرض منتجات الفضة
- **إدارة الفئات**: تنظيم المنتجات في فئات
- **سلة التسوق**: إضافة المنتجات إلى سلة التسوق وإدارتها
- **نظام الطلبات**: إنشاء ومتابعة الطلبات
- **لوحة تحكم المسؤول**: إحصائيات وإدارة شاملة للمتجر
- **واجهة مستخدم سهلة**: تصميم متجاوب مع جميع الأجهزة

## متطلبات النظام

- Python 3.6 أو أحدث
- Flask
- SQLAlchemy
- Flask-Login
- Werkzeug
- Pillow (لمعالجة الصور)

## التثبيت

1. قم بإنشاء بيئة افتراضية جديدة:

```bash
python -m venv venv
```

2. تفعيل البيئة الافتراضية:

- في Windows:
```bash
venv\Scripts\activate
```

- في macOS/Linux:
```bash
source venv/bin/activate
```

3. تثبيت المكتبات المطلوبة:

```bash
pip install flask flask-sqlalchemy flask-login werkzeug pillow
```

4. تشغيل التطبيق:

```bash
python app.py
```

5. افتح المتصفح وانتقل إلى `http://127.0.0.1:5000`

## هيكل المشروع

```
silver-jewelry-store/
├── app.py                  # ملف التطبيق الرئيسي
├── config.py               # إعدادات التطبيق
├── models.py               # نماذج قاعدة البيانات
├── static/                 # الملفات الثابتة
│   ├── css/                # ملفات CSS
│   ├── js/                 # ملفات JavaScript
│   ├── img/                # الصور
│   └── uploads/            # مجلد تحميل صور المنتجات
└── templates/              # قوالب HTML
    ├── layout.html         # القالب الرئيسي
    ├── index.html          # الصفحة الرئيسية
    ├── login.html          # صفحة تسجيل الدخول
    ├── dashboard.html      # لوحة التحكم
    ├── products.html       # قائمة المنتجات
    ├── add_product.html    # إضافة منتج
    ├── edit_product.html   # تعديل منتج
    ├── product_details.html # تفاصيل المنتج
    ├── cart.html           # سلة التسوق
    ├── checkout.html       # إتمام الشراء
    ├── orders.html         # إدارة الطلبات
    ├── order_details.html  # تفاصيل الطلب
    ├── order_confirmation.html # تأكيد الطلب
    └── categories.html     # إدارة الفئات
```

## بيانات تسجيل الدخول الافتراضية

- **المسؤول**:
  - اسم المستخدم: admin
  - كلمة المرور: admin123

## الإعدادات

يمكنك تعديل الإعدادات في ملف `config.py`، بما في ذلك:

- مفتاح السر للتطبيق
- إعدادات قاعدة البيانات
- مجلد تحميل الصور
- الحد الأقصى لحجم الصور

## المساهمة

نرحب بمساهماتكم! يرجى إرسال طلبات السحب أو فتح مشكلة لمناقشة التغييرات المقترحة.

## الترخيص

هذا المشروع مرخص بموجب [MIT License](LICENSE).