{% extends 'layout.html' %}

{% block title %}إدارة الفئات{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row">
        <div class="col-md-4">
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-light">
                    <h5 class="mb-0">إضافة فئة جديدة</h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ url_for('add_category') }}">
                        <div class="mb-3">
                            <label for="name" class="form-label">اسم الفئة</label>
                            <input type="text" class="form-control" id="name" name="name" required>
                        </div>
                        <div class="mb-3">
                            <label for="description" class="form-label">وصف الفئة</label>
                            <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                        </div>
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-plus-circle me-1"></i> إضافة فئة
                        </button>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="col-md-8">
            <div class="card shadow-sm">
                <div class="card-header bg-light d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">قائمة الفئات</h5>
                    <span class="badge bg-primary">{{ categories|length }} فئة</span>
                </div>
                <div class="card-body p-0">
                    {% if categories %}
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>#</th>
                                    <th>اسم الفئة</th>
                                    <th>الوصف</th>
                                    <th>عدد المنتجات</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for category in categories %}
                                <tr>
                                    <td>{{ category.id }}</td>
                                    <td>{{ category.name }}</td>
                                    <td>
                                        {% if category.description %}
                                        {{ category.description|truncate(50) }}
                                        {% else %}
                                        <span class="text-muted">لا يوجد وصف</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="badge bg-info">{{ category.products|length }}</span>
                                    </td>
                                    <td>
                                        <div class="btn-group">
                                            <button type="button" class="btn btn-sm btn-outline-primary" data-bs-toggle="modal" data-bs-target="#editCategory{{ category.id }}">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button type="button" class="btn btn-sm btn-outline-danger" data-bs-toggle="modal" data-bs-target="#deleteCategory{{ category.id }}">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                
                                <!-- Edit Category Modal -->
                                <div class="modal fade" id="editCategory{{ category.id }}" tabindex="-1" aria-labelledby="editCategoryLabel{{ category.id }}" aria-hidden="true">
                                    <div class="modal-dialog">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h5 class="modal-title" id="editCategoryLabel{{ category.id }}">تعديل الفئة</h5>
                                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                            </div>
                                            <form method="POST" action="{{ url_for('edit_category', category_id=category.id) }}">
                                                <div class="modal-body">
                                                    <div class="mb-3">
                                                        <label for="edit_name{{ category.id }}" class="form-label">اسم الفئة</label>
                                                        <input type="text" class="form-control" id="edit_name{{ category.id }}" name="name" value="{{ category.name }}" required>
                                                    </div>
                                                    <div class="mb-3">
                                                        <label for="edit_description{{ category.id }}" class="form-label">وصف الفئة</label>
                                                        <textarea class="form-control" id="edit_description{{ category.id }}" name="description" rows="3">{{ category.description }}</textarea>
                                                    </div>
                                                </div>
                                                <div class="modal-footer">
                                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                                                    <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                                                </div>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Delete Category Modal -->
                                <div class="modal fade" id="deleteCategory{{ category.id }}" tabindex="-1" aria-labelledby="deleteCategoryLabel{{ category.id }}" aria-hidden="true">
                                    <div class="modal-dialog">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h5 class="modal-title" id="deleteCategoryLabel{{ category.id }}">تأكيد الحذف</h5>
                                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                            </div>
                                            <div class="modal-body">
                                                <p>هل أنت متأكد من حذف الفئة "{{ category.name }}"؟</p>
                                                {% if category.products|length > 0 %}
                                                <div class="alert alert-warning">
                                                    <i class="fas fa-exclamation-triangle me-2"></i> تحذير: هذه الفئة تحتوي على {{ category.products|length }} منتج. حذف هذه الفئة سيؤدي إلى إزالة تصنيف هذه المنتجات.
                                                </div>
                                                {% endif %}
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                                                <form method="POST" action="{{ url_for('delete_category', category_id=category.id) }}" class="d-inline">
                                                    <button type="submit" class="btn btn-danger">حذف</button>
                                                </form>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-4">
                        <div class="mb-3">
                            <i class="fas fa-folder-open fa-3x text-muted"></i>
                        </div>
                        <h5>لا توجد فئات</h5>
                        <p class="text-muted">قم بإضافة فئات جديدة لتنظيم منتجاتك</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}