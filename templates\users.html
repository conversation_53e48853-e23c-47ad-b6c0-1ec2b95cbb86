{% extends 'layout.html' %}

{% block title %}إدارة المستخدمين{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // وظيفة البحث في جدول المستخدمين
        const searchInput = document.getElementById('userSearchInput');
        const usersTable = document.getElementById('usersTable');
        const tableRows = usersTable.getElementsByTagName('tbody')[0].getElementsByTagName('tr');
        
        searchInput.addEventListener('keyup', function() {
            const searchTerm = searchInput.value.toLowerCase();
            
            for (let i = 0; i < tableRows.length; i++) {
                const row = tableRows[i];
                const username = row.cells[1].textContent.toLowerCase();
                const email = row.cells[2].textContent.toLowerCase();
                
                if (username.includes(searchTerm) || email.includes(searchTerm)) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            }
        });
        
        // تنبيه عند حذف مستخدم
        const deleteButtons = document.querySelectorAll('[data-bs-target^="#deleteUser"]');
        deleteButtons.forEach(button => {
            button.addEventListener('click', function() {
                const modal = document.querySelector(button.getAttribute('data-bs-target'));
                const deleteForm = modal.querySelector('form');
                
                deleteForm.addEventListener('submit', function(e) {
                    if (!confirm('هل أنت متأكد من حذف هذا المستخدم؟ لا يمكن التراجع عن هذا الإجراء.')) {
                        e.preventDefault();
                    }
                });
            });
        });
    });
</script>
{% endblock %}

{% block content %}
<div class="container py-4">
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{{ url_for('dashboard') }}">لوحة التحكم</a></li>
            <li class="breadcrumb-item active" aria-current="page">إدارة المستخدمين</li>
        </ol>
    </nav>
    
    <div class="card shadow-sm mb-4">
        <div class="card-body">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-0"><i class="fas fa-users text-primary me-2"></i> إدارة المستخدمين</h2>
                    <p class="text-muted mt-2">إدارة حسابات المستخدمين وصلاحياتهم</p>
                </div>
                <div>
                    <a href="{{ url_for('add_user') }}" class="btn btn-primary">
                        <i class="fas fa-user-plus me-1"></i> إضافة مستخدم جديد
                    </a>
                </div>
            </div>
        </div>
    </div>

    {% if users %}
    <div class="card shadow-sm">
        <div class="card-header bg-white py-3">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h5 class="mb-0">قائمة المستخدمين</h5>
                </div>
                <div class="col-md-6">
                    <div class="input-group">
                        <input type="text" id="userSearchInput" class="form-control" placeholder="بحث عن مستخدم...">
                        <span class="input-group-text"><i class="fas fa-search"></i></span>
                    </div>
                </div>
            </div>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0" id="usersTable">
                    <thead class="table-light">
                        <tr>
                            <th>#</th>
                            <th>اسم المستخدم</th>
                            <th>البريد الإلكتروني</th>
                            <th>نوع المستخدم</th>
                            <th>تاريخ التسجيل</th>
                            <th>آخر تسجيل دخول</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for user in users %}
                        <tr>
                            <td>{{ user.id }}</td>
                            <td>{{ user.username }}</td>
                            <td>{{ user.email }}</td>
                            <td>
                                {% if user.is_admin %}
                                <span class="badge bg-danger">مسؤول</span>
                                {% else %}
                                <span class="badge bg-info">مستخدم</span>
                                {% endif %}
                            </td>
                            <td>{{ user.created_at.strftime('%Y-%m-%d') }}</td>
                            <td>
                                {% if user.last_login %}
                                {{ user.last_login.strftime('%Y-%m-%d %H:%M') }}
                                {% else %}
                                <span class="text-muted">لم يسجل الدخول بعد</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if user.is_active %}
                                <span class="badge bg-success">نشط</span>
                                {% else %}
                                <span class="badge bg-secondary">غير نشط</span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="btn-group">
                                    <a href="{{ url_for('edit_user', user_id=user.id) }}" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-edit"></i> تعديل
                                    </a>
                                    {% if not user.is_admin or current_user.id != user.id %}
                                    <button type="button" class="btn btn-sm btn-outline-danger" data-bs-toggle="modal" data-bs-target="#deleteUser{{ user.id }}">
                                        <i class="fas fa-trash"></i> حذف
                                    </button>
                                    {% endif %}
                                </div>
                            </td>
                        </tr>
                        
                        <!-- Edit User Modal -->
                        <div class="modal fade" id="editUser{{ user.id }}" tabindex="-1" aria-labelledby="editUserLabel{{ user.id }}" aria-hidden="true">
                            <div class="modal-dialog">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h5 class="modal-title" id="editUserLabel{{ user.id }}">تعديل المستخدم</h5>
                                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                    </div>
                                    <form method="POST" action="{{ url_for('edit_user', user_id=user.id) }}">
                                        <div class="modal-body">
                                            <div class="mb-3">
                                                <label for="username{{ user.id }}" class="form-label">اسم المستخدم</label>
                                                <input type="text" class="form-control" id="username{{ user.id }}" name="username" value="{{ user.username }}" required>
                                            </div>
                                            <div class="mb-3">
                                                <label for="email{{ user.id }}" class="form-label">البريد الإلكتروني</label>
                                                <input type="email" class="form-control" id="email{{ user.id }}" name="email" value="{{ user.email }}" required>
                                            </div>
                                            <div class="mb-3">
                                                <label for="password{{ user.id }}" class="form-label">كلمة المرور (اتركها فارغة إذا لم ترغب في تغييرها)</label>
                                                <input type="password" class="form-control" id="password{{ user.id }}" name="password">
                                            </div>
                                            <div class="mb-3 form-check">
                                                <input type="checkbox" class="form-check-input" id="is_admin{{ user.id }}" name="is_admin" {% if user.is_admin %}checked{% endif %} {% if current_user.id == user.id %}disabled{% endif %}>
                                                <label class="form-check-label" for="is_admin{{ user.id }}">مسؤول</label>
                                                {% if current_user.id == user.id %}
                                                <input type="hidden" name="is_admin" value="1">
                                                <small class="text-muted d-block">لا يمكنك تغيير صلاحيات حسابك الحالي</small>
                                                {% endif %}
                                            </div>
                                            <div class="mb-3 form-check">
                                                <input type="checkbox" class="form-check-input" id="is_active{{ user.id }}" name="is_active" {% if user.is_active %}checked{% endif %} {% if current_user.id == user.id %}disabled{% endif %}>
                                                <label class="form-check-label" for="is_active{{ user.id }}">نشط</label>
                                                {% if current_user.id == user.id %}
                                                <input type="hidden" name="is_active" value="1">
                                                <small class="text-muted d-block">لا يمكنك تعطيل حسابك الحالي</small>
                                                {% endif %}
                                            </div>
                                        </div>
                                        <div class="modal-footer">
                                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                                            <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Delete User Modal -->
                        {% if not user.is_admin or current_user.id != user.id %}
                        <div class="modal fade" id="deleteUser{{ user.id }}" tabindex="-1" aria-labelledby="deleteUserLabel{{ user.id }}" aria-hidden="true">
                            <div class="modal-dialog">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h5 class="modal-title" id="deleteUserLabel{{ user.id }}">تأكيد الحذف</h5>
                                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                    </div>
                                    <div class="modal-body">
                                        <p>هل أنت متأكد من حذف المستخدم "{{ user.username }}"؟</p>
                                        <div class="alert alert-warning">
                                            <i class="fas fa-exclamation-triangle me-2"></i> تحذير: سيتم حذف جميع بيانات هذا المستخدم بشكل نهائي.
                                        </div>
                                    </div>
                                    <div class="modal-footer">
                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                                        <form method="POST" action="{{ url_for('delete_user', user_id=user.id) }}" class="d-inline">
                                            <button type="submit" class="btn btn-danger">حذف</button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endif %}
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    {% else %}
    <div class="alert alert-info">
        <i class="fas fa-info-circle me-2"></i> لا يوجد مستخدمين حالياً غير حسابك.
    </div>
    {% endif %}
</div>

<!-- Add User Modal -->
<div class="modal fade" id="addUserModal" tabindex="-1" aria-labelledby="addUserModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addUserModalLabel">إضافة مستخدم جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="POST" action="{{ url_for('add_user') }}">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="username" class="form-label">اسم المستخدم</label>
                        <input type="text" class="form-control" id="username" name="username" required>
                    </div>
                    <div class="mb-3">
                        <label for="email" class="form-label">البريد الإلكتروني</label>
                        <input type="email" class="form-control" id="email" name="email" required>
                    </div>
                    <div class="mb-3">
                        <label for="password" class="form-label">كلمة المرور</label>
                        <input type="password" class="form-control" id="password" name="password" required>
                    </div>
                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="is_admin" name="is_admin">
                        <label class="form-check-label" for="is_admin">مسؤول</label>
                    </div>
                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="is_active" name="is_active" checked>
                        <label class="form-check-label" for="is_active">نشط</label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">إضافة</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}