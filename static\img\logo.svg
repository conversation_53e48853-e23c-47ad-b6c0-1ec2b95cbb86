<svg width="200" height="60" xmlns="http://www.w3.org/2000/svg">
  <!-- Silver gradient for the jewelry part -->
  <defs>
    <linearGradient id="silver-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#f0f0f0" />
      <stop offset="50%" stop-color="#c0c0c0" />
      <stop offset="100%" stop-color="#a0a0a0" />
    </linearGradient>
    
    <!-- Shine effect -->
    <filter id="shine" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur in="SourceGraphic" stdDeviation="1" result="blur"/>
      <feComposite in="blur" in2="SourceGraphic" operator="lighter"/>
    </filter>
  </defs>
  
  <!-- Logo Background (optional, transparent) -->
  <rect width="200" height="60" fill="transparent"/>
  
  <!-- Silver Jewelry Icon -->
  <g transform="translate(10, 10)">
    <!-- Stylized ring/jewelry piece -->
    <circle cx="20" cy="20" r="15" fill="url(#silver-gradient)" stroke="#666" stroke-width="1"/>
    <circle cx="20" cy="20" r="10" fill="none" stroke="#888" stroke-width="1"/>
    
    <!-- Diamond/gem effect -->
    <polygon points="20,10 25,20 20,30 15,20" fill="white" opacity="0.7" filter="url(#shine)"/>
    
    <!-- Shine details -->
    <circle cx="15" cy="15" r="2" fill="white" opacity="0.8"/>
  </g>
  
  <!-- Text: Silver Store -->
  <g transform="translate(50, 35)">
    <!-- Silver text -->
    <text font-family="Arial, sans-serif" font-size="22" font-weight="bold" fill="#333">
      Silver<tspan fill="#9e9e9e">Store</tspan>
    </text>
    
    <!-- Arabic text below -->
    <text font-family="Arial, sans-serif" font-size="12" fill="#666" y="15">
      فضيات الملك
    </text>
  </g>
</svg>