{% extends 'layout.html' %}

{% block title %}إتمام الطلب{% endblock %}

{% block content %}
<h1 class="mb-4">إتمام الطلب</h1>

<div class="row">
    <!-- نموذج معلومات العميل -->
    <div class="col-lg-8 mb-4">
        <div class="card shadow-sm">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">معلومات الشحن والدفع</h5>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('checkout') }}">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="name" class="form-label">الاسم الكامل</label>
                            <input type="text" class="form-control" id="name" name="name" required>
                        </div>
                        <div class="col-md-6">
                            <label for="email" class="form-label">البريد الإلكتروني</label>
                            <input type="email" class="form-control" id="email" name="email" required>
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="phone" class="form-label">رقم الهاتف</label>
                            <input type="tel" class="form-control" id="phone" name="phone" required>
                        </div>
                        <div class="col-md-6">
                            <label for="city" class="form-label">المدينة</label>
                            <input type="text" class="form-control" id="city" name="city" required>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="address" class="form-label">العنوان التفصيلي</label>
                        <textarea class="form-control" id="address" name="address" rows="3" required></textarea>
                    </div>
                    
                    <hr class="my-4">
                    
                    <h5 class="mb-3">طريقة الدفع</h5>
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="payment_method" id="payment_cash" value="cash" checked>
                            <label class="form-check-label" for="payment_cash">
                                الدفع عند الاستلام
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="payment_method" id="payment_card" value="card">
                            <label class="form-check-label" for="payment_card">
                                بطاقة ائتمان
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="payment_method" id="payment_bank" value="bank">
                            <label class="form-check-label" for="payment_bank">
                                تحويل بنكي
                            </label>
                        </div>
                    </div>
                    
                    <div id="card_details" class="card-details mb-3" style="display: none;">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="card_number" class="form-label">رقم البطاقة</label>
                                <input type="text" class="form-control" id="card_number" placeholder="XXXX XXXX XXXX XXXX">
                            </div>
                            <div class="col-md-6">
                                <label for="card_name" class="form-label">الاسم على البطاقة</label>
                                <input type="text" class="form-control" id="card_name">
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <label for="card_expiry" class="form-label">تاريخ الانتهاء</label>
                                <input type="text" class="form-control" id="card_expiry" placeholder="MM/YY">
                            </div>
                            <div class="col-md-6">
                                <label for="card_cvv" class="form-label">رمز الأمان (CVV)</label>
                                <input type="text" class="form-control" id="card_cvv" placeholder="XXX">
                            </div>
                        </div>
                    </div>
                    
                    <div id="bank_details" class="bank-details mb-3" style="display: none;">
                        <div class="alert alert-info">
                            <p class="mb-1">يرجى تحويل المبلغ إلى الحساب التالي:</p>
                            <p class="mb-1">اسم البنك: بنك الرياض</p>
                            <p class="mb-1">اسم الحساب: فضيات الملك</p>
                            <p class="mb-1">رقم الحساب: SA0000000000000000000000</p>
                            <p class="mb-0">بعد التحويل، يرجى إرسال صورة من إيصال التحويل إلى البريد الإلكتروني: <EMAIL></p>
                        </div>
                    </div>
                    
                    <div class="form-check mb-4">
                        <input class="form-check-input" type="checkbox" id="terms" required>
                        <label class="form-check-label" for="terms">
                            أوافق على <a href="#">الشروط والأحكام</a> و<a href="#">سياسة الخصوصية</a>
                        </label>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('cart') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-1"></i> العودة إلى السلة
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-check me-1"></i> تأكيد الطلب
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- ملخص الطلب -->
    <div class="col-lg-4">
        <div class="card shadow-sm mb-4">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">ملخص الطلب</h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <h6>المنتجات ({{ cart_items|length }}):</h6>
                    <ul class="list-group list-group-flush">
                        {% for item in cart_items %}
                        <li class="list-group-item d-flex justify-content-between align-items-center px-0">
                            <div>
                                <span>{{ item.product.name }}</span>
                                <small class="d-block text-muted">{{ item.quantity }} × {{ item.product.price }} ريال</small>
                            </div>
                            <span>{{ (item.product.price * item.quantity)|round(2) }} ريال</span>
                        </li>
                        {% endfor %}
                    </ul>
                </div>
                
                <div class="d-flex justify-content-between mb-2">
                    <span>إجمالي المنتجات:</span>
                    <span>{{ total }} ريال</span>
                </div>
                <div class="d-flex justify-content-between mb-2">
                    <span>الشحن:</span>
                    <span>مجاني</span>
                </div>
                <hr>
                <div class="d-flex justify-content-between mb-0 fw-bold">
                    <span>الإجمالي:</span>
                    <span>{{ total }} ريال</span>
                </div>
            </div>
        </div>
        
        <div class="card shadow-sm">
            <div class="card-body">
                <h6>ملاحظات:</h6>
                <ul class="mb-0">
                    <li>سيتم شحن الطلب خلال 2-3 أيام عمل.</li>
                    <li>يمكنك تتبع حالة الطلب من خلال حسابك الشخصي.</li>
                    <li>للاستفسارات، يرجى التواصل على الرقم: **********</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // إظهار/إخفاء تفاصيل طرق الدفع
        const paymentMethods = document.querySelectorAll('input[name="payment_method"]');
        const cardDetails = document.getElementById('card_details');
        const bankDetails = document.getElementById('bank_details');
        
        paymentMethods.forEach(method => {
            method.addEventListener('change', function() {
                if (this.value === 'card') {
                    cardDetails.style.display = 'block';
                    bankDetails.style.display = 'none';
                } else if (this.value === 'bank') {
                    cardDetails.style.display = 'none';
                    bankDetails.style.display = 'block';
                } else {
                    cardDetails.style.display = 'none';
                    bankDetails.style.display = 'none';
                }
            });
        });
    });
</script>
{% endblock %}