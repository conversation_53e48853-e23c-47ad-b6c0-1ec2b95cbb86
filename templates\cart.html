{% extends 'layout.html' %}

{% block title %}سلة التسوق{% endblock %}

{% block content %}
<h1 class="mb-4">سلة التسوق</h1>

{% if cart_items %}
<div class="row">
    <!-- قائمة المنتجات في السلة -->
    <div class="col-lg-8 mb-4">
        <div class="card shadow-sm">
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>المنتج</th>
                                <th>السعر</th>
                                <th>الكمية</th>
                                <th>المجموع</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for item in cart_items %}
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        {% if item.product.image %}
                                        <img src="{{ url_for('static', filename='uploads/' + item.product.image) }}" alt="{{ item.product.name }}" width="50" height="50" class="img-thumbnail me-2">
                                        {% else %}
                                        <img src="{{ url_for('static', filename='img/no-image.jpg') }}" alt="صورة غير متوفرة" width="50" height="50" class="img-thumbnail me-2">
                                        {% endif %}
                                        <div>
                                            <h6 class="mb-0">{{ item.product.name }}</h6>
                                        </div>
                                    </div>
                                </td>
                                <td>{{ item.product.price }} ريال</td>
                                <td>
                                    <div class="input-group input-group-sm" style="width: 100px;">
                                        <button class="btn btn-outline-secondary" type="button" onclick="updateQuantity(this, -1)">-</button>
                                        <input type="number" class="form-control text-center" value="{{ item.quantity }}" min="1" max="{{ item.product.stock }}" data-item-id="{{ item.id }}" readonly>
                                        <button class="btn btn-outline-secondary" type="button" onclick="updateQuantity(this, 1)">+</button>
                                    </div>
                                </td>
                                <td>{{ (item.product.price * item.quantity)|round(2) }} ريال</td>
                                <td>
                                    <form action="{{ url_for('remove_from_cart', item_id=item.id) }}" method="post">
                                        <button type="submit" class="btn btn-sm btn-danger">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <!-- ملخص الطلب -->
    <div class="col-lg-4">
        <div class="card shadow-sm">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">ملخص الطلب</h5>
            </div>
            <div class="card-body">
                <div class="d-flex justify-content-between mb-2">
                    <span>إجمالي المنتجات:</span>
                    <span>{{ total }} ريال</span>
                </div>
                <div class="d-flex justify-content-between mb-2">
                    <span>الشحن:</span>
                    <span>مجاني</span>
                </div>
                <hr>
                <div class="d-flex justify-content-between mb-3 fw-bold">
                    <span>الإجمالي:</span>
                    <span>{{ total }} ريال</span>
                </div>
                <div class="d-grid gap-2">
                    <a href="{{ url_for('checkout') }}" class="btn btn-primary">
                        <i class="fas fa-credit-card me-1"></i> إتمام الطلب
                    </a>
                    <a href="{{ url_for('home') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-1"></i> متابعة التسوق
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% else %}
<div class="card shadow-sm">
    <div class="card-body text-center py-5">
        <i class="fas fa-shopping-cart fa-4x text-muted mb-3"></i>
        <h3>سلة التسوق فارغة</h3>
        <p class="mb-4">لم تقم بإضافة أي منتجات إلى سلة التسوق بعد.</p>
        <a href="{{ url_for('home') }}" class="btn btn-primary">
            <i class="fas fa-shopping-bag me-1"></i> تسوق الآن
        </a>
    </div>
</div>
{% endif %}
{% endblock %}

{% block scripts %}
<script>
    // تحديث الكمية في سلة التسوق
    function updateQuantity(button, change) {
        const input = button.parentNode.querySelector('input');
        const currentValue = parseInt(input.value);
        const maxValue = parseInt(input.getAttribute('max'));
        const itemId = input.getAttribute('data-item-id');
        
        let newValue = currentValue + change;
        if (newValue < 1) newValue = 1;
        if (newValue > maxValue) {
            newValue = maxValue;
            alert('عذراً، الكمية المتوفرة هي ' + maxValue + ' فقط.');
        }
        
        if (newValue !== currentValue) {
            input.value = newValue;
            
            // إرسال طلب AJAX لتحديث الكمية في قاعدة البيانات
            // هذا مثال فقط، يجب تنفيذ المسار المناسب في الخلفية
            /*
            fetch('/cart/update/' + itemId, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ quantity: newValue }),
            })
            .then(response => response.json())
            .then(data => {
                // تحديث الصفحة أو تحديث المجموع فقط
                window.location.reload();
            });
            */
            
            // لأغراض العرض، نقوم بإعادة تحميل الصفحة
            window.location.reload();
        }
    }
</script>
{% endblock %}