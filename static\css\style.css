/* Silver Jewelry Store Custom Styles */

/* General Styles */
body {
    font-family: '<PERSON><PERSON><PERSON>', sans-serif;
    background-color: #f8f9fa;
    color: #333;
    direction: rtl;
    text-align: right;
}

.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
    color: #333;
}

.navbar-brand span {
    color: #9e9e9e;
}

/* Hero Section */
.hero-section {
    background: linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)), url('../img/silver-bg.jpg');
    background-size: cover;
    background-position: center;
    color: white;
    padding: 100px 0;
    margin-bottom: 40px;
    text-align: center;
}

.hero-section h1 {
    font-size: 3rem;
    font-weight: bold;
    margin-bottom: 20px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.hero-section p {
    font-size: 1.2rem;
    margin-bottom: 30px;
    max-width: 700px;
    margin-left: auto;
    margin-right: auto;
}

/* Product Cards */
.product-card {
    border: none;
    transition: transform 0.3s, box-shadow 0.3s;
    margin-bottom: 20px;
    overflow: hidden;
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.product-img-container {
    height: 200px;
    overflow: hidden;
    position: relative;
}

.product-img-container img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s;
}

.product-card:hover .product-img-container img {
    transform: scale(1.1);
}

.product-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    z-index: 10;
}

.product-card .card-title {
    font-weight: 600;
    margin-bottom: 5px;
}

.product-card .price {
    font-weight: bold;
    color: #333;
    font-size: 1.1rem;
}

.product-card .old-price {
    text-decoration: line-through;
    color: #999;
    font-size: 0.9rem;
    margin-right: 8px;
}

/* Category Pills */
.category-pill {
    display: inline-block;
    padding: 8px 20px;
    border-radius: 50px;
    background-color: white;
    color: #333;
    margin: 5px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    transition: all 0.3s;
    text-decoration: none;
}

.category-pill:hover {
    background-color: #333;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.category-pill.active {
    background-color: #333;
    color: white;
}

/* Product Details */
.product-details-img {
    max-height: 400px;
    object-fit: contain;
}

.product-details-title {
    font-size: 1.8rem;
    font-weight: bold;
    margin-bottom: 10px;
}

.product-details-price {
    font-size: 1.5rem;
    font-weight: bold;
    color: #333;
    margin-bottom: 15px;
}

.product-details-category {
    display: inline-block;
    padding: 5px 15px;
    background-color: #f0f0f0;
    border-radius: 20px;
    font-size: 0.9rem;
    margin-bottom: 15px;
}

.quantity-selector {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
}

.quantity-selector button {
    width: 40px;
    height: 40px;
    border: 1px solid #ddd;
    background-color: #f8f9fa;
    font-size: 1.2rem;
}

.quantity-selector input {
    width: 60px;
    height: 40px;
    border: 1px solid #ddd;
    text-align: center;
    margin: 0 5px;
}

/* Cart Styles */
.cart-item {
    margin-bottom: 20px;
    padding-bottom: 20px;
    border-bottom: 1px solid #eee;
}

.cart-item-img {
    width: 80px;
    height: 80px;
    object-fit: cover;
}

.cart-summary {
    background-color: #f8f9fa;
    padding: 20px;
    border-radius: 5px;
}

/* Dashboard Styles */
.dashboard-stat-card {
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s;
}

.dashboard-stat-card:hover {
    transform: translateY(-5px);
}

.dashboard-stat-card .icon {
    font-size: 2.5rem;
    margin-bottom: 15px;
}

.dashboard-stat-card .stat-value {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 5px;
}

.dashboard-stat-card .stat-label {
    font-size: 1rem;
    color: #6c757d;
}

/* Login Form */
.login-form-container {
    max-width: 400px;
    margin: 50px auto;
    padding: 30px;
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.login-logo {
    text-align: center;
    margin-bottom: 30px;
}

.login-logo h1 {
    font-weight: bold;
    color: #333;
}

.login-logo span {
    color: #9e9e9e;
}

/* Footer */
footer {
    background-color: #333;
    color: white;
    padding: 40px 0 20px;
    margin-top: 50px;
}

footer h5 {
    font-weight: bold;
    margin-bottom: 20px;
    color: #fff;
}

footer ul {
    padding-right: 0;
}

footer ul li {
    margin-bottom: 10px;
}

footer a {
    color: #ccc;
    text-decoration: none;
    transition: color 0.3s;
}

footer a:hover {
    color: white;
}

.social-icons a {
    display: inline-block;
    width: 40px;
    height: 40px;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    text-align: center;
    line-height: 40px;
    margin-left: 10px;
    transition: background-color 0.3s;
}

.social-icons a:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

.footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: 20px;
    margin-top: 30px;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .hero-section {
        padding: 60px 0;
    }
    
    .hero-section h1 {
        font-size: 2rem;
    }
    
    .product-img-container {
        height: 180px;
    }
    
    .dashboard-stat-card .icon {
        font-size: 2rem;
    }
    
    .dashboard-stat-card .stat-value {
        font-size: 1.5rem;
    }
}

/* Print Styles */
@media print {
    .navbar, .footer, .btn, .no-print {
        display: none !important;
    }
    
    body {
        background-color: white;
    }
    
    .container {
        width: 100%;
        max-width: 100%;
    }
    
    .card {
        box-shadow: none !important;
        border: 1px solid #ddd;
    }
}