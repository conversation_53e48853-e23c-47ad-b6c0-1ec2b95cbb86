{% extends 'layout.html' %}

{% block title %}تأكيد الطلب{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card shadow-sm">
            <div class="card-body text-center py-5">
                <div class="mb-4">
                    <i class="fas fa-check-circle fa-5x text-success"></i>
                </div>
                <h2 class="mb-3">تم تأكيد طلبك بنجاح!</h2>
                <p class="lead mb-1">رقم الطلب: <strong>#{{ order.id }}</strong></p>
                <p class="mb-4">تم إرسال تفاصيل الطلب إلى بريدك الإلكتروني: {{ order.customer_email }}</p>
                
                <div class="card mb-4">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">تفاصيل الطلب</h5>
                    </div>
                    <div class="card-body">
                        <div class="row mb-3">
                            <div class="col-md-6 text-start">
                                <p class="mb-1"><strong>تاريخ الطلب:</strong></p>
                                <p>{{ order.created_at.strftime('%Y-%m-%d %H:%M') }}</p>
                            </div>
                            <div class="col-md-6 text-start">
                                <p class="mb-1"><strong>حالة الطلب:</strong></p>
                                <p>
                                    {% if order.status == 'pending' %}
                                    <span class="badge bg-warning">قيد الانتظار</span>
                                    {% elif order.status == 'processing' %}
                                    <span class="badge bg-info">قيد المعالجة</span>
                                    {% elif order.status == 'shipped' %}
                                    <span class="badge bg-primary">تم الشحن</span>
                                    {% elif order.status == 'delivered' %}
                                    <span class="badge bg-success">تم التسليم</span>
                                    {% elif order.status == 'cancelled' %}
                                    <span class="badge bg-danger">ملغي</span>
                                    {% else %}
                                    <span class="badge bg-secondary">{{ order.status }}</span>
                                    {% endif %}
                                </p>
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-6 text-start">
                                <p class="mb-1"><strong>معلومات العميل:</strong></p>
                                <p class="mb-1">{{ order.customer_name }}</p>
                                <p class="mb-1">{{ order.customer_email }}</p>
                                <p>{{ order.customer_phone }}</p>
                            </div>
                            <div class="col-md-6 text-start">
                                <p class="mb-1"><strong>عنوان الشحن:</strong></p>
                                <p>{{ order.customer_address }}</p>
                            </div>
                        </div>
                        
                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <thead class="table-light">
                                    <tr>
                                        <th>المنتج</th>
                                        <th>السعر</th>
                                        <th>الكمية</th>
                                        <th>المجموع</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for item in order.items %}
                                    <tr>
                                        <td>{{ item.product.name }}</td>
                                        <td>{{ item.price }} ريال</td>
                                        <td>{{ item.quantity }}</td>
                                        <td>{{ (item.price * item.quantity)|round(2) }} ريال</td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                                <tfoot>
                                    <tr>
                                        <td colspan="3" class="text-end"><strong>الإجمالي:</strong></td>
                                        <td><strong>{{ order.total_amount }} ريال</strong></td>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    </div>
                </div>
                
                <div class="d-flex justify-content-center gap-3">
                    <a href="{{ url_for('home') }}" class="btn btn-primary">
                        <i class="fas fa-home me-1"></i> العودة للرئيسية
                    </a>
                    <a href="#" class="btn btn-outline-secondary">
                        <i class="fas fa-print me-1"></i> طباعة الفاتورة
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // يمكن إضافة سكربت لطباعة الفاتورة
    document.addEventListener('DOMContentLoaded', function() {
        const printButton = document.querySelector('.btn-outline-secondary');
        if (printButton) {
            printButton.addEventListener('click', function(e) {
                e.preventDefault();
                window.print();
            });
        }
    });
</script>
{% endblock %}