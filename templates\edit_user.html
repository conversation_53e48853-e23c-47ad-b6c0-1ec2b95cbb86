{% extends 'layout.html' %}

{% block title %}تعديل المستخدم{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-user-edit me-2"></i>تعديل المستخدم</h5>
                </div>
                <div class="card-body">
                    {% with messages = get_flashed_messages() %}
                        {% if messages %}
                            {% for message in messages %}
                                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                                    {{ message }}
                                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                                </div>
                            {% endfor %}
                        {% endif %}
                    {% endwith %}
                    
                    <form method="POST" action="{{ url_for('edit_user', user_id=user.id) }}">
                        <div class="mb-3">
                            <label for="username" class="form-label">اسم المستخدم</label>
                            <input type="text" class="form-control" id="username" name="username" value="{{ user.username }}" required>
                        </div>
                        <div class="mb-3">
                            <label for="email" class="form-label">البريد الإلكتروني</label>
                            <input type="email" class="form-control" id="email" name="email" value="{{ user.email }}" required>
                        </div>
                        <div class="mb-3">
                            <label for="password" class="form-label">كلمة المرور (اتركها فارغة إذا لم ترغب في تغييرها)</label>
                            <input type="password" class="form-control" id="password" name="password">
                        </div>
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="is_admin" name="is_admin" {% if user.is_admin %}checked{% endif %} {% if current_user.id == user.id %}disabled{% endif %}>
                            <label class="form-check-label" for="is_admin">مسؤول</label>
                            {% if current_user.id == user.id %}
                                <input type="hidden" name="is_admin" value="1">
                                <small class="text-muted d-block">لا يمكنك تغيير صلاحيات حسابك الحالي</small>
                            {% endif %}
                        </div>
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="is_active" name="is_active" {% if user.is_active %}checked{% endif %} {% if current_user.id == user.id %}disabled{% endif %}>
                            <label class="form-check-label" for="is_active">نشط</label>
                            {% if current_user.id == user.id %}
                                <input type="hidden" name="is_active" value="1">
                                <small class="text-muted d-block">لا يمكنك تعطيل حسابك الحالي</small>
                            {% endif %}
                        </div>
                        <div class="mb-3">
                            <label class="form-label">آخر تسجيل دخول</label>
                            <p class="form-control-static">{{ user.last_login.strftime('%Y-%m-%d %H:%M:%S') if user.last_login else 'لم يسجل الدخول بعد' }}</p>
                        </div>
                        <div class="d-flex justify-content-between">
                            <a href="{{ url_for('users') }}" class="btn btn-secondary"><i class="fas fa-arrow-left me-1"></i> العودة</a>
                            <button type="submit" class="btn btn-primary"><i class="fas fa-save me-1"></i> حفظ</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}