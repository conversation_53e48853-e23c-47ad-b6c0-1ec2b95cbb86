<svg width="1200" height="600" xmlns="http://www.w3.org/2000/svg">
  <!-- Background gradient -->
  <defs>
    <linearGradient id="bg-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#2c3e50" />
      <stop offset="100%" stop-color="#1a1a1a" />
    </linearGradient>
    
    <!-- Silver pattern -->
    <pattern id="silver-pattern" x="0" y="0" width="60" height="60" patternUnits="userSpaceOnUse">
      <rect width="60" height="60" fill="#333" opacity="0.3"/>
      <path d="M0 0L60 60M60 0L0 60" stroke="#999" stroke-width="1" opacity="0.2"/>
    </pattern>
    
    <!-- Shine effect -->
    <filter id="shine" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur in="SourceGraphic" stdDeviation="10" result="blur"/>
      <feComposite in="blur" in2="SourceGraphic" operator="lighter"/>
    </filter>
  </defs>
  
  <!-- Main background -->
  <rect width="1200" height="600" fill="url(#bg-gradient)"/>
  
  <!-- Silver pattern overlay -->
  <rect width="1200" height="600" fill="url(#silver-pattern)" opacity="0.5"/>
  
  <!-- Decorative elements -->
  <g opacity="0.7">
    <!-- Silver circles -->
    <circle cx="200" cy="150" r="80" fill="none" stroke="#c0c0c0" stroke-width="2"/>
    <circle cx="1000" cy="450" r="100" fill="none" stroke="#a0a0a0" stroke-width="2"/>
    <circle cx="600" cy="300" r="150" fill="none" stroke="#d0d0d0" stroke-width="1" opacity="0.3"/>
    
    <!-- Silver jewelry shapes -->
    <path d="M300,200 C350,150 400,250 450,200 S500,100 550,150 S600,250 650,200" 
          stroke="#e0e0e0" stroke-width="3" fill="none" opacity="0.6"/>
    <path d="M700,350 C750,300 800,400 850,350 S900,250 950,300" 
          stroke="#c0c0c0" stroke-width="3" fill="none" opacity="0.6"/>
    
    <!-- Abstract silver elements -->
    <polygon points="150,400 200,450 100,450" fill="#a0a0a0" opacity="0.4"/>
    <polygon points="900,150 950,200 850,200" fill="#b0b0b0" opacity="0.4"/>
    <rect x="400" y="400" width="50" height="50" fill="#d0d0d0" opacity="0.3"/>
    <rect x="800" y="250" width="30" height="30" fill="#e0e0e0" opacity="0.3"/>
  </g>
  
  <!-- Shine effects -->
  <g filter="url(#shine)" opacity="0.4">
    <circle cx="300" cy="200" r="5" fill="white"/>
    <circle cx="700" cy="350" r="5" fill="white"/>
    <circle cx="900" cy="150" r="5" fill="white"/>
    <circle cx="200" cy="450" r="5" fill="white"/>
    <circle cx="600" cy="300" r="8" fill="white"/>
    <circle cx="1000" cy="400" r="5" fill="white"/>
  </g>
</svg>