{% extends 'layout.html' %}

{% block title %}تقارير المبيعات{% endblock %}

{% block styles %}
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/chart.js@3.7.1/dist/chart.min.css">
{% endblock %}

{% block content %}
<div class="container py-4">
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{{ url_for('dashboard') }}">لوحة التحكم</a></li>
            <li class="breadcrumb-item active" aria-current="page">تقارير المبيعات</li>
        </ol>
    </nav>
    
    <div class="card shadow-sm mb-4">
        <div class="card-body">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-0"><i class="fas fa-chart-line text-primary me-2"></i> تقارير المبيعات</h2>
                    <p class="text-muted mt-2">تحليل المبيعات والإيرادات</p>
                </div>
                <div>
                    <div class="btn-group">
                        <button type="button" class="btn btn-outline-primary" id="btnDaily">يومي</button>
                        <button type="button" class="btn btn-outline-primary" id="btnWeekly">أسبوعي</button>
                        <button type="button" class="btn btn-outline-primary active" id="btnMonthly">شهري</button>
                        <button type="button" class="btn btn-outline-primary" id="btnYearly">سنوي</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-8">
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-white py-3">
                    <h5 class="mb-0">تحليل المبيعات</h5>
                </div>
                <div class="card-body">
                    <canvas id="salesChart" height="300"></canvas>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-white py-3">
                    <h5 class="mb-0">ملخص المبيعات</h5>
                </div>
                <div class="card-body">
                    <div class="mb-4">
                        <div class="d-flex justify-content-between mb-1">
                            <span>إجمالي المبيعات</span>
                            <span class="fw-bold">{{ total_sales|default('0.00', true) }} ريال</span>
                        </div>
                        <div class="progress" style="height: 10px;">
                            <div class="progress-bar bg-primary" role="progressbar" style="width: 100%" aria-valuenow="100" aria-valuemin="0" aria-valuemax="100"></div>
                        </div>
                    </div>
                    <div class="mb-4">
                        <div class="d-flex justify-content-between mb-1">
                            <span>عدد الطلبات</span>
                            <span class="fw-bold">{{ orders_count|default('0', true) }}</span>
                        </div>
                        <div class="progress" style="height: 10px;">
                            <div class="progress-bar bg-success" role="progressbar" style="width: 75%" aria-valuenow="75" aria-valuemin="0" aria-valuemax="100"></div>
                        </div>
                    </div>
                    <div class="mb-4">
                        <div class="d-flex justify-content-between mb-1">
                            <span>متوسط قيمة الطلب</span>
                            <span class="fw-bold">{{ average_order|default('0.00', true) }} ريال</span>
                        </div>
                        <div class="progress" style="height: 10px;">
                            <div class="progress-bar bg-info" role="progressbar" style="width: 50%" aria-valuenow="50" aria-valuemin="0" aria-valuemax="100"></div>
                        </div>
                    </div>
                    <div class="mb-4">
                        <div class="d-flex justify-content-between mb-1">
                            <span>نسبة النمو</span>
                            <span class="fw-bold text-success">+{{ growth_rate|default('0', true) }}%</span>
                        </div>
                        <div class="progress" style="height: 10px;">
                            <div class="progress-bar bg-warning" role="progressbar" style="width: 25%" aria-valuenow="25" aria-valuemin="0" aria-valuemax="100"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-6">
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-white py-3">
                    <h5 class="mb-0">أفضل المنتجات مبيعاً</h5>
                </div>
                <div class="card-body">
                    <canvas id="topProductsChart" height="250"></canvas>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-white py-3">
                    <h5 class="mb-0">توزيع المبيعات حسب الفئات</h5>
                </div>
                <div class="card-body">
                    <canvas id="categoriesChart" height="250"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/chart.js@3.7.1/dist/chart.min.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // بيانات المبيعات (يمكن استبدالها ببيانات حقيقية من الخادم)
        const salesData = {
            labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'],
            datasets: [{
                label: 'المبيعات (ريال)',
                data: [12000, 19000, 15000, 25000, 22000, 30000, 28000, 26000, 29000, 32000, 35000, 40000],
                backgroundColor: 'rgba(54, 162, 235, 0.2)',
                borderColor: 'rgba(54, 162, 235, 1)',
                borderWidth: 2,
                tension: 0.4
            }]
        };

        // بيانات أفضل المنتجات مبيعاً
        const topProductsData = {
            labels: ['خاتم الماس', 'سوار فضة', 'قلادة ذهبية', 'أقراط فضية', 'خاتم زفاف'],
            datasets: [{
                label: 'المبيعات',
                data: [150, 120, 100, 80, 50],
                backgroundColor: [
                    'rgba(255, 99, 132, 0.7)',
                    'rgba(54, 162, 235, 0.7)',
                    'rgba(255, 206, 86, 0.7)',
                    'rgba(75, 192, 192, 0.7)',
                    'rgba(153, 102, 255, 0.7)'
                ],
                borderWidth: 1
            }]
        };

        // بيانات توزيع المبيعات حسب الفئات
        const categoriesData = {
            labels: ['خواتم', 'أساور', 'قلائد', 'أقراط', 'ساعات', 'أخرى'],
            datasets: [{
                label: 'المبيعات حسب الفئة',
                data: [35, 25, 20, 15, 10, 5],
                backgroundColor: [
                    'rgba(255, 99, 132, 0.7)',
                    'rgba(54, 162, 235, 0.7)',
                    'rgba(255, 206, 86, 0.7)',
                    'rgba(75, 192, 192, 0.7)',
                    'rgba(153, 102, 255, 0.7)',
                    'rgba(255, 159, 64, 0.7)'
                ],
                borderWidth: 1
            }]
        };

        // إنشاء الرسوم البيانية
        const salesChart = new Chart(
            document.getElementById('salesChart').getContext('2d'),
            {
                type: 'line',
                data: salesData,
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            position: 'top',
                        },
                        title: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            }
        );

        const topProductsChart = new Chart(
            document.getElementById('topProductsChart').getContext('2d'),
            {
                type: 'bar',
                data: topProductsData,
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            }
        );

        const categoriesChart = new Chart(
            document.getElementById('categoriesChart').getContext('2d'),
            {
                type: 'doughnut',
                data: categoriesData,
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            position: 'right'
                        }
                    }
                }
            }
        );

        // تبديل عرض البيانات حسب الفترة الزمنية
        document.getElementById('btnDaily').addEventListener('click', function() {
            updateActiveButton(this);
            updateChartData(salesChart, ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15', '16', '17', '18', '19', '20', '21', '22', '23', '24', '25', '26', '27', '28', '29', '30'], 
                            [1200, 1500, 1800, 1600, 2000, 1900, 1700, 2100, 2300, 2200, 2400, 2600, 2500, 2700, 2900, 3000, 2800, 3100, 3300, 3200, 3400, 3600, 3500, 3700, 3900, 4000, 3800, 4100, 4300, 4200]);
        });

        document.getElementById('btnWeekly').addEventListener('click', function() {
            updateActiveButton(this);
            updateChartData(salesChart, ['الأسبوع 1', 'الأسبوع 2', 'الأسبوع 3', 'الأسبوع 4', 'الأسبوع 5'], 
                            [8000, 12000, 10000, 15000, 14000]);
        });

        document.getElementById('btnMonthly').addEventListener('click', function() {
            updateActiveButton(this);
            updateChartData(salesChart, ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'], 
                            [12000, 19000, 15000, 25000, 22000, 30000, 28000, 26000, 29000, 32000, 35000, 40000]);
        });

        document.getElementById('btnYearly').addEventListener('click', function() {
            updateActiveButton(this);
            updateChartData(salesChart, ['2018', '2019', '2020', '2021', '2022', '2023'], 
                            [150000, 200000, 180000, 250000, 300000, 350000]);
        });

        // تحديث زر النشط
        function updateActiveButton(button) {
            document.querySelectorAll('.btn-group .btn').forEach(btn => {
                btn.classList.remove('active');
            });
            button.classList.add('active');
        }

        // تحديث بيانات الرسم البياني
        function updateChartData(chart, labels, data) {
            chart.data.labels = labels;
            chart.data.datasets[0].data = data;
            chart.update();
        }
    });
</script>
{% endblock %}