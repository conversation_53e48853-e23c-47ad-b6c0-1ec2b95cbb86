import React, { createContext, useContext, useState, useEffect } from 'react';

const StudioContext = createContext();

export const useStudio = () => {
  const context = useContext(StudioContext);
  if (!context) {
    throw new Error('useStudio must be used within a StudioProvider');
  }
  return context;
};

export const StudioProvider = ({ children }) => {
  // باقات الخدمات
  const [packages, setPackages] = useState([
    {
      id: 1,
      name: 'باقة التصميم الأساسية',
      description: 'تصميم لوجو + بطاقة عمل + غلاف فيسبوك',
      price: 500,
      features: ['تصميم لوجو احترافي', 'بطاقة عمل', 'غلاف فيسبوك', 'مراجعتين مجانيتين'],
      category: 'تصميم',
      image: '/images/basic-design.jpg',
      popular: false
    },
    {
      id: 2,
      name: 'باقة التصميم المتقدمة',
      description: 'هوية بصرية كاملة + تصميمات سوشيال ميديا',
      price: 1200,
      features: ['هوية بصرية كاملة', '10 تصميمات سوشيال ميديا', 'بروشور', 'كارت شخصي', '5 مراجعات مجانية'],
      category: 'تصميم',
      image: '/images/advanced-design.jpg',
      popular: true
    },
    {
      id: 3,
      name: 'باقة الإعلانات الرقمية',
      description: 'حملة إعلانية متكاملة على منصات التواصل',
      price: 2000,
      features: ['استراتيجية إعلانية', 'تصميم الإعلانات', 'إدارة الحملة لمدة شهر', 'تقارير أداء'],
      category: 'إعلانات',
      image: '/images/digital-ads.jpg',
      popular: false
    },
    {
      id: 4,
      name: 'باقة المواقع الإلكترونية',
      description: 'تصميم وتطوير موقع إلكتروني احترافي',
      price: 3500,
      features: ['تصميم مخصص', 'برمجة احترافية', 'تحسين محركات البحث', 'استضافة سنة مجانية'],
      category: 'مواقع',
      image: '/images/website.jpg',
      popular: true
    }
  ]);

  // الطلبات
  const [orders, setOrders] = useState([
    {
      id: 1,
      clientId: 2,
      clientName: 'عميل تجريبي',
      clientPhone: '01234567890',
      packageId: 1,
      packageName: 'باقة التصميم الأساسية',
      amount: 500,
      status: 'pending', // pending, confirmed, in_progress, completed, cancelled
      paymentStatus: 'pending', // pending, paid, failed
      paymentMethod: 'vodafone_cash',
      vodafoneNumber: '01140403120',
      orderDate: new Date().toISOString(),
      notes: 'أريد تصميم لوجو لمطعم'
    }
  ]);

  // إحصائيات الأرباح
  const [earnings, setEarnings] = useState({
    totalRevenue: 15000,
    monthlyRevenue: 3500,
    weeklyRevenue: 800,
    dailyRevenue: 150,
    totalOrders: 25,
    pendingOrders: 3,
    completedOrders: 20,
    cancelledOrders: 2
  });

  // إضافة طلب جديد
  const addOrder = (orderData) => {
    const newOrder = {
      id: orders.length + 1,
      ...orderData,
      orderDate: new Date().toISOString(),
      status: 'pending',
      paymentStatus: 'pending'
    };
    
    setOrders(prev => [...prev, newOrder]);
    return { success: true, order: newOrder };
  };

  // تحديث حالة الطلب
  const updateOrderStatus = (orderId, status) => {
    setOrders(prev => 
      prev.map(order => 
        order.id === orderId ? { ...order, status } : order
      )
    );
  };

  // تحديث حالة الدفع
  const updatePaymentStatus = (orderId, paymentStatus) => {
    setOrders(prev => 
      prev.map(order => 
        order.id === orderId ? { ...order, paymentStatus } : order
      )
    );
    
    // تحديث الأرباح عند تأكيد الدفع
    if (paymentStatus === 'paid') {
      const order = orders.find(o => o.id === orderId);
      if (order) {
        setEarnings(prev => ({
          ...prev,
          totalRevenue: prev.totalRevenue + order.amount,
          monthlyRevenue: prev.monthlyRevenue + order.amount,
          weeklyRevenue: prev.weeklyRevenue + order.amount,
          dailyRevenue: prev.dailyRevenue + order.amount
        }));
      }
    }
  };

  // إضافة باقة جديدة
  const addPackage = (packageData) => {
    const newPackage = {
      id: packages.length + 1,
      ...packageData
    };
    
    setPackages(prev => [...prev, newPackage]);
    return { success: true, package: newPackage };
  };

  // تحديث باقة
  const updatePackage = (packageId, updatedData) => {
    setPackages(prev => 
      prev.map(pkg => 
        pkg.id === packageId ? { ...pkg, ...updatedData } : pkg
      )
    );
    return { success: true };
  };

  // حذف باقة
  const deletePackage = (packageId) => {
    setPackages(prev => prev.filter(pkg => pkg.id !== packageId));
    return { success: true };
  };

  const value = {
    packages,
    orders,
    earnings,
    addOrder,
    updateOrderStatus,
    updatePaymentStatus,
    addPackage,
    updatePackage,
    deletePackage,
    // معلومات الاتصال
    contactInfo: {
      phone: '01211157539',
      vodafoneNumber: '01140403120',
      email: '<EMAIL>',
      address: 'القاهرة، مصر'
    }
  };

  return (
    <StudioContext.Provider value={value}>
      {children}
    </StudioContext.Provider>
  );
};
