import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  FaHome, FaBox, FaShoppingCart, FaChartLine, FaCog, FaUsers,
  FaEye, FaEdit, FaTrash, FaPlus, FaCheck, FaTimes, FaPhone
} from 'react-icons/fa';
import { useStudio } from '../context/StudioContext';
import { useAuth } from '../context/AuthContext';

const AdminDashboard = () => {
  const { 
    packages, orders, earnings, 
    updateOrderStatus, updatePaymentStatus,
    addPackage, updatePackage, deletePackage,
    contactInfo
  } = useStudio();
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState('overview');
  const [showAddPackage, setShowAddPackage] = useState(false);
  const [editingPackage, setEditingPackage] = useState(null);

  // إحصائيات سريعة
  const quickStats = {
    totalOrders: orders.length,
    pendingOrders: orders.filter(o => o.status === 'pending').length,
    completedOrders: orders.filter(o => o.status === 'completed').length,
    totalRevenue: earnings.totalRevenue,
    monthlyRevenue: earnings.monthlyRevenue,
    totalPackages: packages.length
  };

  const handleOrderStatusUpdate = (orderId, newStatus) => {
    updateOrderStatus(orderId, newStatus);
  };

  const handlePaymentStatusUpdate = (orderId, newStatus) => {
    updatePaymentStatus(orderId, newStatus);
  };

  const renderOverview = () => (
    <motion.div 
      className="admin-overview"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <h2>نظرة عامة</h2>
      
      {/* Quick Stats */}
      <div className="stats-grid">
        <div className="stat-card revenue">
          <div className="stat-icon">
            <FaChartLine />
          </div>
          <div className="stat-content">
            <h3>{quickStats.totalRevenue.toLocaleString()}</h3>
            <p>إجمالي الإيرادات</p>
            <span className="stat-change positive">+{earnings.monthlyRevenue} هذا الشهر</span>
          </div>
        </div>

        <div className="stat-card orders">
          <div className="stat-icon">
            <FaShoppingCart />
          </div>
          <div className="stat-content">
            <h3>{quickStats.totalOrders}</h3>
            <p>إجمالي الطلبات</p>
            <span className="stat-change">{quickStats.pendingOrders} قيد الانتظار</span>
          </div>
        </div>

        <div className="stat-card packages">
          <div className="stat-icon">
            <FaBox />
          </div>
          <div className="stat-content">
            <h3>{quickStats.totalPackages}</h3>
            <p>الباقات المتاحة</p>
            <span className="stat-change">جميع الفئات</span>
          </div>
        </div>

        <div className="stat-card completed">
          <div className="stat-icon">
            <FaCheck />
          </div>
          <div className="stat-content">
            <h3>{quickStats.completedOrders}</h3>
            <p>طلبات مكتملة</p>
            <span className="stat-change positive">معدل إنجاز 95%</span>
          </div>
        </div>
      </div>

      {/* Recent Orders */}
      <div className="recent-orders">
        <h3>الطلبات الأخيرة</h3>
        <div className="orders-table">
          <table>
            <thead>
              <tr>
                <th>رقم الطلب</th>
                <th>العميل</th>
                <th>الباقة</th>
                <th>المبلغ</th>
                <th>الحالة</th>
                <th>الإجراءات</th>
              </tr>
            </thead>
            <tbody>
              {orders.slice(0, 5).map(order => (
                <tr key={order.id}>
                  <td>#{order.id}</td>
                  <td>{order.clientName}</td>
                  <td>{order.packageName}</td>
                  <td>{order.amount} جنيه</td>
                  <td>
                    <span className={`status ${order.status}`}>
                      {order.status === 'pending' ? 'قيد الانتظار' :
                       order.status === 'confirmed' ? 'مؤكد' :
                       order.status === 'in_progress' ? 'قيد التنفيذ' :
                       order.status === 'completed' ? 'مكتمل' : 'ملغي'}
                    </span>
                  </td>
                  <td>
                    <button 
                      className="btn-icon view"
                      title="عرض التفاصيل"
                    >
                      <FaEye />
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </motion.div>
  );

  const renderOrders = () => (
    <motion.div 
      className="admin-orders"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <div className="section-header">
        <h2>إدارة الطلبات</h2>
        <div className="orders-filters">
          <select>
            <option value="all">جميع الطلبات</option>
            <option value="pending">قيد الانتظار</option>
            <option value="confirmed">مؤكدة</option>
            <option value="in_progress">قيد التنفيذ</option>
            <option value="completed">مكتملة</option>
          </select>
        </div>
      </div>

      <div className="orders-table">
        <table>
          <thead>
            <tr>
              <th>رقم الطلب</th>
              <th>العميل</th>
              <th>الهاتف</th>
              <th>الباقة</th>
              <th>المبلغ</th>
              <th>حالة الطلب</th>
              <th>حالة الدفع</th>
              <th>التاريخ</th>
              <th>الإجراءات</th>
            </tr>
          </thead>
          <tbody>
            {orders.map(order => (
              <tr key={order.id}>
                <td>#{order.id}</td>
                <td>{order.clientName}</td>
                <td>
                  <a href={`tel:${order.clientPhone}`}>
                    <FaPhone /> {order.clientPhone}
                  </a>
                </td>
                <td>{order.packageName}</td>
                <td>{order.amount} جنيه</td>
                <td>
                  <select 
                    value={order.status}
                    onChange={(e) => handleOrderStatusUpdate(order.id, e.target.value)}
                    className={`status-select ${order.status}`}
                  >
                    <option value="pending">قيد الانتظار</option>
                    <option value="confirmed">مؤكد</option>
                    <option value="in_progress">قيد التنفيذ</option>
                    <option value="completed">مكتمل</option>
                    <option value="cancelled">ملغي</option>
                  </select>
                </td>
                <td>
                  <select 
                    value={order.paymentStatus}
                    onChange={(e) => handlePaymentStatusUpdate(order.id, e.target.value)}
                    className={`payment-select ${order.paymentStatus}`}
                  >
                    <option value="pending">قيد الانتظار</option>
                    <option value="paid">مدفوع</option>
                    <option value="failed">فشل</option>
                  </select>
                </td>
                <td>{new Date(order.orderDate).toLocaleDateString('ar-EG')}</td>
                <td>
                  <div className="action-buttons">
                    <button className="btn-icon view" title="عرض التفاصيل">
                      <FaEye />
                    </button>
                    <button className="btn-icon edit" title="تعديل">
                      <FaEdit />
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </motion.div>
  );

  const renderPackages = () => (
    <motion.div 
      className="admin-packages"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <div className="section-header">
        <h2>إدارة الباقات</h2>
        <button 
          className="btn btn-primary"
          onClick={() => setShowAddPackage(true)}
        >
          <FaPlus /> إضافة باقة جديدة
        </button>
      </div>

      <div className="packages-grid">
        {packages.map(pkg => (
          <div key={pkg.id} className="admin-package-card">
            <div className="package-header">
              <h3>{pkg.name}</h3>
              <div className="package-actions">
                <button 
                  className="btn-icon edit"
                  onClick={() => setEditingPackage(pkg)}
                  title="تعديل"
                >
                  <FaEdit />
                </button>
                <button 
                  className="btn-icon delete"
                  onClick={() => deletePackage(pkg.id)}
                  title="حذف"
                >
                  <FaTrash />
                </button>
              </div>
            </div>
            <p>{pkg.description}</p>
            <div className="package-details">
              <div className="package-price">{pkg.price} جنيه</div>
              <div className="package-category">{pkg.category}</div>
              {pkg.popular && <div className="package-badge">الأكثر طلباً</div>}
            </div>
            <div className="package-features">
              <ul>
                {pkg.features.slice(0, 3).map((feature, idx) => (
                  <li key={idx}>{feature}</li>
                ))}
              </ul>
            </div>
          </div>
        ))}
      </div>
    </motion.div>
  );

  const renderEarnings = () => (
    <motion.div 
      className="admin-earnings"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <h2>تقارير الأرباح</h2>
      
      <div className="earnings-grid">
        <div className="earning-card">
          <h3>الإيرادات اليومية</h3>
          <div className="earning-amount">{earnings.dailyRevenue} جنيه</div>
        </div>
        <div className="earning-card">
          <h3>الإيرادات الأسبوعية</h3>
          <div className="earning-amount">{earnings.weeklyRevenue} جنيه</div>
        </div>
        <div className="earning-card">
          <h3>الإيرادات الشهرية</h3>
          <div className="earning-amount">{earnings.monthlyRevenue} جنيه</div>
        </div>
        <div className="earning-card">
          <h3>إجمالي الإيرادات</h3>
          <div className="earning-amount">{earnings.totalRevenue} جنيه</div>
        </div>
      </div>

      <div className="earnings-chart">
        <h3>رسم بياني للأرباح</h3>
        <div className="chart-placeholder">
          <p>سيتم إضافة الرسم البياني هنا</p>
        </div>
      </div>
    </motion.div>
  );

  const sidebarItems = [
    { id: 'overview', label: 'نظرة عامة', icon: <FaHome /> },
    { id: 'orders', label: 'الطلبات', icon: <FaShoppingCart /> },
    { id: 'packages', label: 'الباقات', icon: <FaBox /> },
    { id: 'earnings', label: 'الأرباح', icon: <FaChartLine /> },
    { id: 'settings', label: 'الإعدادات', icon: <FaCog /> }
  ];

  return (
    <div className="admin-dashboard">
      <div className="admin-header">
        <h1>لوحة تحكم Y-Studio</h1>
        <div className="admin-user-info">
          <span>مرحباً، {user?.name}</span>
        </div>
      </div>

      <div className="admin-content">
        <div className="admin-sidebar">
          <nav className="admin-nav">
            {sidebarItems.map(item => (
              <button
                key={item.id}
                className={`nav-item ${activeTab === item.id ? 'active' : ''}`}
                onClick={() => setActiveTab(item.id)}
              >
                {item.icon}
                <span>{item.label}</span>
              </button>
            ))}
          </nav>
        </div>

        <div className="admin-main">
          {activeTab === 'overview' && renderOverview()}
          {activeTab === 'orders' && renderOrders()}
          {activeTab === 'packages' && renderPackages()}
          {activeTab === 'earnings' && renderEarnings()}
        </div>
      </div>
    </div>
  );
};

export default AdminDashboard;
