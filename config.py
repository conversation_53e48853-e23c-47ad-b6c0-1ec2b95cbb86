import os

class Config:
    # إعدادات التطبيق
    SECRET_KEY = 'مفتاح-سري-آمن-للتطبيق'
    SQLALCHEMY_DATABASE_URI = 'sqlite:///silver_store.db'
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    
    # إعدادات تحميل الصور
    UPLOAD_FOLDER = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'static/uploads')
    ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif'}
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16 ميجابايت كحد أقصى