{% extends 'layout.html' %}

{% block title %}تفاصيل الطلب #{{ order.id }}{% endblock %}

{% block content %}
<div class="container py-4">
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{{ url_for('dashboard') }}">لوحة التحكم</a></li>
            <li class="breadcrumb-item"><a href="{{ url_for('orders') }}">الطلبات</a></li>
            <li class="breadcrumb-item active" aria-current="page">تفاصيل الطلب #{{ order.id }}</li>
        </ol>
    </nav>

    <div class="row">
        <div class="col-md-8">
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-light d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">تفاصيل الطلب #{{ order.id }}</h5>
                    <div>
                        <button class="btn btn-sm btn-outline-secondary" onclick="window.print()">
                            <i class="fas fa-print me-1"></i> طباعة
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h6 class="text-muted mb-2">معلومات الطلب</h6>
                            <p class="mb-1"><strong>رقم الطلب:</strong> #{{ order.id }}</p>
                            <p class="mb-1"><strong>تاريخ الطلب:</strong> {{ order.created_at.strftime('%Y-%m-%d %H:%M') }}</p>
                            <p class="mb-1">
                                <strong>حالة الطلب:</strong>
                                {% if order.status == 'pending' %}
                                <span class="badge bg-warning">قيد الانتظار</span>
                                {% elif order.status == 'processing' %}
                                <span class="badge bg-info">قيد المعالجة</span>
                                {% elif order.status == 'shipped' %}
                                <span class="badge bg-primary">تم الشحن</span>
                                {% elif order.status == 'delivered' %}
                                <span class="badge bg-success">تم التسليم</span>
                                {% elif order.status == 'cancelled' %}
                                <span class="badge bg-danger">ملغي</span>
                                {% else %}
                                <span class="badge bg-secondary">{{ order.status }}</span>
                                {% endif %}
                            </p>
                            <p class="mb-1"><strong>طريقة الدفع:</strong> {{ order.payment_method }}</p>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-muted mb-2">معلومات العميل</h6>
                            <p class="mb-1"><strong>الاسم:</strong> {{ order.customer_name }}</p>
                            <p class="mb-1"><strong>البريد الإلكتروني:</strong> {{ order.customer_email }}</p>
                            <p class="mb-1"><strong>رقم الهاتف:</strong> {{ order.customer_phone }}</p>
                            <p class="mb-1"><strong>العنوان:</strong> {{ order.customer_address }}</p>
                        </div>
                    </div>

                    <h6 class="text-muted mb-3">المنتجات</h6>
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead class="table-light">
                                <tr>
                                    <th>المنتج</th>
                                    <th>السعر</th>
                                    <th>الكمية</th>
                                    <th>المجموع</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in order.items %}
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            {% if item.product.image %}
                                            <img src="{{ url_for('static', filename='uploads/' + item.product.image) }}" alt="{{ item.product.name }}" class="img-thumbnail me-2" style="width: 50px; height: 50px; object-fit: cover;">
                                            {% else %}
                                            <div class="bg-light me-2 d-flex align-items-center justify-content-center" style="width: 50px; height: 50px;">
                                                <i class="fas fa-image text-muted"></i>
                                            </div>
                                            {% endif %}
                                            <div>
                                                <p class="mb-0">{{ item.product.name }}</p>
                                                {% if item.product.category %}
                                                <small class="text-muted">{{ item.product.category.name }}</small>
                                                {% endif %}
                                            </div>
                                        </div>
                                    </td>
                                    <td>{{ item.price }} ريال</td>
                                    <td>{{ item.quantity }}</td>
                                    <td>{{ (item.price * item.quantity)|round(2) }} ريال</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                            <tfoot>
                                <tr>
                                    <td colspan="3" class="text-end"><strong>الإجمالي:</strong></td>
                                    <td><strong>{{ order.total_amount }} ريال</strong></td>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-light">
                    <h5 class="mb-0">تحديث الحالة</h5>
                </div>
                <div class="card-body">
                    <form action="{{ url_for('update_order_status', order_id=order.id) }}" method="POST">
                        <div class="mb-3">
                            <label for="status" class="form-label">حالة الطلب</label>
                            <select name="status" id="status" class="form-select">
                                <option value="pending" {% if order.status == 'pending' %}selected{% endif %}>قيد الانتظار</option>
                                <option value="processing" {% if order.status == 'processing' %}selected{% endif %}>قيد المعالجة</option>
                                <option value="shipped" {% if order.status == 'shipped' %}selected{% endif %}>تم الشحن</option>
                                <option value="delivered" {% if order.status == 'delivered' %}selected{% endif %}>تم التسليم</option>
                                <option value="cancelled" {% if order.status == 'cancelled' %}selected{% endif %}>ملغي</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="notes" class="form-label">ملاحظات (اختياري)</label>
                            <textarea name="notes" id="notes" class="form-control" rows="3"></textarea>
                        </div>
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-save me-1"></i> تحديث الحالة
                        </button>
                    </form>
                </div>
            </div>

            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="mb-0">سجل الطلب</h5>
                </div>
                <div class="card-body p-0">
                    <ul class="list-group list-group-flush">
                        <li class="list-group-item">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <p class="mb-0"><strong>تم إنشاء الطلب</strong></p>
                                    <small class="text-muted">تم إنشاء الطلب بنجاح</small>
                                </div>
                                <small class="text-muted">{{ order.created_at.strftime('%Y-%m-%d %H:%M') }}</small>
                            </div>
                        </li>
                        {% if order.status != 'pending' %}
                        <li class="list-group-item">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <p class="mb-0"><strong>تحديث الحالة</strong></p>
                                    <small class="text-muted">تم تحديث حالة الطلب إلى 
                                        {% if order.status == 'processing' %}
                                        قيد المعالجة
                                        {% elif order.status == 'shipped' %}
                                        تم الشحن
                                        {% elif order.status == 'delivered' %}
                                        تم التسليم
                                        {% elif order.status == 'cancelled' %}
                                        ملغي
                                        {% endif %}
                                    </small>
                                </div>
                                <small class="text-muted">{{ order.updated_at.strftime('%Y-%m-%d %H:%M') }}</small>
                            </div>
                        </li>
                        {% endif %}
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}