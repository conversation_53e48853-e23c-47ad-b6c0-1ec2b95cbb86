{% extends 'layout.html' %}

{% block title %}{{ product.name }}{% endblock %}

{% block content %}
<nav aria-label="breadcrumb" class="mb-4">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{{ url_for('home') }}">الرئيسية</a></li>
        <li class="breadcrumb-item active" aria-current="page">{{ product.name }}</li>
    </ol>
</nav>

<div class="row">
    <!-- صورة المنتج -->
    <div class="col-md-5 mb-4">
        <div class="card shadow-sm">
            {% if product.image %}
            <img src="{{ url_for('static', filename='uploads/' + product.image) }}" class="card-img-top" alt="{{ product.name }}">
            {% else %}
            <img src="{{ url_for('static', filename='img/no-image.jpg') }}" class="card-img-top" alt="صورة غير متوفرة">
            {% endif %}
        </div>
    </div>
    
    <!-- تفاصيل المنتج -->
    <div class="col-md-7 mb-4">
        <div class="card shadow-sm">
            <div class="card-body">
                <h2 class="card-title mb-3">{{ product.name }}</h2>
                <p class="card-text fs-4 text-primary fw-bold mb-3">{{ product.price }} ريال</p>
                
                <div class="mb-3">
                    <h5>الوصف:</h5>
                    <p class="card-text">{{ product.description }}</p>
                </div>
                
                <div class="mb-3">
                    <h5>الفئة:</h5>
                    <p class="card-text">{{ product.category }}</p>
                </div>
                
                <div class="mb-4">
                    <h5>حالة المخزون:</h5>
                    {% if product.stock > 10 %}
                    <span class="badge bg-success">متوفر ({{ product.stock }} قطعة)</span>
                    {% elif product.stock > 0 %}
                    <span class="badge bg-warning">متوفر بكمية محدودة ({{ product.stock }} قطعة)</span>
                    {% else %}
                    <span class="badge bg-danger">غير متوفر حالياً</span>
                    {% endif %}
                </div>
                
                {% if session.get('user_id') and product.stock > 0 %}
                <form action="{{ url_for('add_to_cart', product_id=product.id) }}" method="post" class="mb-3">
                    <div class="row g-3 align-items-center">
                        <div class="col-auto">
                            <label for="quantity" class="col-form-label">الكمية:</label>
                        </div>
                        <div class="col-auto">
                            <input type="number" id="quantity" name="quantity" class="form-control" min="1" max="{{ product.stock }}" value="1" required>
                        </div>
                        <div class="col-auto">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-cart-plus me-1"></i> إضافة إلى السلة
                            </button>
                        </div>
                    </div>
                </form>
                {% elif not session.get('user_id') %}
                <div class="alert alert-info">
                    <a href="{{ url_for('login') }}">سجل دخولك</a> لإضافة هذا المنتج إلى سلة التسوق.
                </div>
                {% endif %}
                
                <!-- أزرار المشاركة -->
                <div class="mt-4">
                    <h5>مشاركة:</h5>
                    <div class="d-flex gap-2">
                        <a href="#" class="btn btn-sm btn-outline-primary">
                            <i class="fab fa-facebook-f"></i>
                        </a>
                        <a href="#" class="btn btn-sm btn-outline-info">
                            <i class="fab fa-twitter"></i>
                        </a>
                        <a href="#" class="btn btn-sm btn-outline-success">
                            <i class="fab fa-whatsapp"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- منتجات مشابهة -->
<section class="my-5">
    <h3 class="mb-4">منتجات مشابهة</h3>
    <div class="row">
        {% for i in range(3) %}
        <div class="col-md-4 mb-4">
            <div class="card h-100 shadow-sm">
                <img src="{{ url_for('static', filename='img/no-image.jpg') }}" class="card-img-top" alt="صورة غير متوفرة">
                <div class="card-body">
                    <h5 class="card-title">منتج مشابه</h5>
                    <p class="card-text">وصف للمنتج المشابه</p>
                    <p class="card-text text-primary fw-bold">100 ريال</p>
                </div>
                <div class="card-footer bg-white border-top-0">
                    <a href="#" class="btn btn-sm btn-outline-primary w-100">عرض التفاصيل</a>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
</section>
{% endblock %}

{% block scripts %}
<script>
    // يمكن إضافة سكربت لعرض الصور بشكل أفضل أو للتفاعل مع المنتج
    document.addEventListener('DOMContentLoaded', function() {
        // مثال: التحقق من الكمية المدخلة
        const quantityInput = document.getElementById('quantity');
        if (quantityInput) {
            quantityInput.addEventListener('change', function() {
                const max = parseInt(this.getAttribute('max'));
                const value = parseInt(this.value);
                if (value > max) {
                    this.value = max;
                    alert('عذراً، الكمية المتوفرة هي ' + max + ' فقط.');
                }
            });
        }
    });
</script>
{% endblock %}