from flask import Flask, render_template, request, redirect, url_for, flash, session
from werkzeug.security import generate_password_hash, check_password_hash
from werkzeug.utils import secure_filename
from models import db, User, Product, Order, OrderItem, Category, CartItem
from config import Config
import os
import datetime

# إنشاء تطبيق Flask
app = Flask(__name__)
app.config.from_object(Config)

# التأكد من وجود مجلد التحميل
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

# تهيئة قاعدة البيانات
db.init_app(app)

# إضافة متغير now إلى جميع القوالب
@app.context_processor
def inject_now():
    return {'now': datetime.datetime.now()}

# التحقق من امتدادات الملفات المسموح بها
def allowed_file(filename):
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in app.config['ALLOWED_EXTENSIONS']

# التحقق من تسجيل الدخول
def login_required(view):
    def wrapped_view(*args, **kwargs):
        if 'user_id' not in session:
            return redirect(url_for('login'))
        return view(*args, **kwargs)
    wrapped_view.__name__ = view.__name__
    return wrapped_view

# التحقق من صلاحيات المسؤول
def admin_required(view):
    def wrapped_view(*args, **kwargs):
        if 'user_id' not in session or not session.get('is_admin'):
            flash('يجب أن تكون مسؤولاً للوصول إلى هذه الصفحة')
            return redirect(url_for('login'))
        return view(*args, **kwargs)
    wrapped_view.__name__ = view.__name__
    return wrapped_view

# الصفحة الرئيسية
@app.route('/')
def home():
    products = Product.query.all()
    categories = Category.query.all()
    favorite_ids = []
    if g.user:
        # الحصول على قائمة المنتجات المفضلة للمستخدم
        favorites = Favorite.query.filter_by(user_id=g.user.id).all()
        favorite_ids = [fav.product_id for fav in favorites]
    return render_template('index.html', products=products, categories=categories, favorite_ids=favorite_ids)

# صفحة تسجيل الدخول
@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        user = User.query.filter_by(username=username).first()
        
        if user and check_password_hash(user.password, password):
            # التحقق من أن الحساب نشط
            if not user.is_active:
                flash('هذا الحساب غير نشط. يرجى التواصل مع المسؤول')
                return render_template('login.html')
                
            # تحديث تاريخ آخر تسجيل دخول
            user.last_login = datetime.datetime.now()
            db.session.commit()
            
            session['user_id'] = user.id
            session['is_admin'] = user.is_admin
            flash('تم تسجيل الدخول بنجاح')
            return redirect(url_for('dashboard'))
        flash('اسم المستخدم أو كلمة المرور غير صحيحة')
    return render_template('login.html')

# صفحة تسجيل الخروج
@app.route('/logout')
def logout():
    session.pop('user_id', None)
    session.pop('is_admin', None)
    flash('تم تسجيل الخروج بنجاح')
    return redirect(url_for('login'))

# صفحة لوحة التحكم
@app.route('/dashboard')
@login_required
def dashboard():
    products = Product.query.all()
    categories = Category.query.all()
    orders = Order.query.all()
    users = User.query.all()
    return render_template('dashboard.html', products=products, categories=categories, orders=orders, users=users)

@app.route('/sales-reports')
@login_required
@admin_required
def sales_reports():
    # Calculate sales statistics
    orders = Order.query.filter_by(status='completed').all()
    total_sales = sum(order.total_amount for order in orders)
    orders_count = len(orders)
    average_order = total_sales / orders_count if orders_count > 0 else 0
    
    # Placeholder for growth rate calculation
    growth_rate = 15  # This would normally be calculated based on historical data
    
    return render_template('sales_reports.html', 
                           total_sales=total_sales, 
                           orders_count=orders_count, 
                           average_order=average_order, 
                           growth_rate=growth_rate)

@app.route('/settings', methods=['GET', 'POST'])
@login_required
@admin_required
def settings():
    # إنشاء قاموس للإعدادات الافتراضية
    default_settings = {
        'site_name': 'King\'s Silverware',
        'site_description': 'متجر مجوهرات فاخرة',
        'contact_email': '<EMAIL>',
        'contact_phone': '+966 12 345 6789',
        'address': 'شارع الملك فهد، الرياض، المملكة العربية السعودية',
        'currency': 'SAR',
        'tax_rate': 15,
        'show_out_of_stock': True,
        'allow_reviews': True,
        'require_account': True,
        'enable_cod': True,
        'enable_bank_transfer': True,
        'enable_credit_card': True,
        'bank_details': 'بنك الراجحي\nرقم الحساب: SA123456789\nاسم المستفيد: King\'s Silverware',
        'shipping_cost': 30,
        'free_shipping_threshold': 500,
        'enable_local_pickup': True
    }
    
    # في المستقبل، يمكن استرجاع الإعدادات من قاعدة البيانات
    settings = default_settings
    
    if request.method == 'POST':
        section = request.form.get('section', '')
        # معالجة الإعدادات المختلفة حسب القسم
        if section == 'general':
            # تحديث الإعدادات العامة
            settings['site_name'] = request.form.get('site_name', settings['site_name'])
            settings['site_description'] = request.form.get('site_description', settings['site_description'])
            settings['contact_email'] = request.form.get('contact_email', settings['contact_email'])
            settings['contact_phone'] = request.form.get('contact_phone', settings['contact_phone'])
            settings['address'] = request.form.get('address', settings['address'])
            
            # معالجة تحميل الملفات (الشعار والأيقونة)
            if 'site_logo' in request.files and request.files['site_logo'].filename:
                logo_file = request.files['site_logo']
                # حفظ الملف وتحديث المسار في الإعدادات
                # هنا يمكن إضافة كود لحفظ الملف
            
            if 'site_favicon' in request.files and request.files['site_favicon'].filename:
                favicon_file = request.files['site_favicon']
                # حفظ الملف وتحديث المسار في الإعدادات
                # هنا يمكن إضافة كود لحفظ الملف
            
            flash('تم تحديث الإعدادات العامة بنجاح', 'success')
            
        elif section == 'store':
            # تحديث إعدادات المتجر
            settings['currency'] = request.form.get('currency', settings['currency'])
            settings['tax_rate'] = float(request.form.get('tax_rate', settings['tax_rate']))
            settings['show_out_of_stock'] = 'show_out_of_stock' in request.form
            settings['allow_reviews'] = 'allow_reviews' in request.form
            settings['require_account'] = 'require_account' in request.form
            
            flash('تم تحديث إعدادات المتجر بنجاح', 'success')
            
        elif section == 'payment':
            # تحديث إعدادات طرق الدفع
            settings['enable_cod'] = 'enable_cod' in request.form
            settings['enable_bank_transfer'] = 'enable_bank_transfer' in request.form
            settings['enable_credit_card'] = 'enable_credit_card' in request.form
            settings['bank_details'] = request.form.get('bank_details', settings['bank_details'])
            
            flash('تم تحديث إعدادات طرق الدفع بنجاح', 'success')
            
        elif section == 'shipping':
            # تحديث إعدادات الشحن
            settings['shipping_cost'] = float(request.form.get('shipping_cost', settings['shipping_cost']))
            settings['free_shipping_threshold'] = float(request.form.get('free_shipping_threshold', settings['free_shipping_threshold']))
            settings['enable_local_pickup'] = 'enable_local_pickup' in request.form
            
            # معالجة الدول المتاحة للشحن
            if 'shipping_countries[]' in request.form:
                settings['shipping_countries'] = request.form.getlist('shipping_countries[]')
            
            flash('تم تحديث إعدادات الشحن بنجاح', 'success')
            
        elif section == 'email':
            # تحديث إعدادات البريد الإلكتروني
            settings['smtp_host'] = request.form.get('smtp_host', '')
            settings['smtp_port'] = int(request.form.get('smtp_port', 587))
            settings['smtp_user'] = request.form.get('smtp_user', '')
            settings['smtp_password'] = request.form.get('smtp_password', '')
            settings['smtp_ssl'] = 'smtp_ssl' in request.form
            settings['email_sender'] = request.form.get('email_sender', '')
            settings['email_sender_name'] = request.form.get('email_sender_name', '')
            
            flash('تم تحديث إعدادات البريد الإلكتروني بنجاح', 'success')
            
        elif section == 'social':
            # تحديث إعدادات وسائل التواصل الاجتماعي
            settings['facebook'] = request.form.get('facebook', '')
            settings['twitter'] = request.form.get('twitter', '')
            settings['instagram'] = request.form.get('instagram', '')
            settings['youtube'] = request.form.get('youtube', '')
            settings['whatsapp'] = request.form.get('whatsapp', '')
            
            flash('تم تحديث إعدادات وسائل التواصل الاجتماعي بنجاح', 'success')
        
        # في المستقبل، يمكن حفظ الإعدادات في قاعدة البيانات
        # هنا يمكن إضافة كود لحفظ الإعدادات
    
    return render_template('settings.html', settings=settings)

# صفحة المنتجات
@app.route('/products')
@login_required
def products():
    all_products = Product.query.all()
    favorite_ids = []
    if g.user:
        # الحصول على قائمة المنتجات المفضلة للمستخدم
        favorites = Favorite.query.filter_by(user_id=g.user.id).all()
        favorite_ids = [fav.product_id for fav in favorites]
    return render_template('products.html', products=all_products, favorite_ids=favorite_ids)

# صفحة إضافة منتج جديد
@app.route('/products/add', methods=['GET', 'POST'])
@admin_required
def add_product():
    categories = Category.query.all()
    if request.method == 'POST':
        name = request.form.get('name')
        description = request.form.get('description')
        price = float(request.form.get('price'))
        category_id = request.form.get('category')
        stock = int(request.form.get('stock'))
        
        # معالجة الصورة
        image_filename = None
        if 'image' in request.files:
            file = request.files['image']
            if file and allowed_file(file.filename):
                filename = secure_filename(file.filename)
                file.save(os.path.join(app.config['UPLOAD_FOLDER'], filename))
                image_filename = filename
        
        new_product = Product(name=name, description=description, price=price, 
                             category=category_id, stock=stock, image=image_filename)
        db.session.add(new_product)
        db.session.commit()
        flash('تم إضافة المنتج بنجاح')
        return redirect(url_for('products'))
    return render_template('add_product.html', categories=categories)

# صفحة تعديل منتج
@app.route('/products/edit/<int:product_id>', methods=['GET', 'POST'])
@admin_required
def edit_product(product_id):
    product = Product.query.get_or_404(product_id)
    categories = Category.query.all()
    if request.method == 'POST':
        product.name = request.form.get('name')
        product.description = request.form.get('description')
        product.price = float(request.form.get('price'))
        product.category = request.form.get('category')
        product.stock = int(request.form.get('stock'))
        
        # معالجة الصورة
        if 'image' in request.files:
            file = request.files['image']
            if file and allowed_file(file.filename):
                filename = secure_filename(file.filename)
                file.save(os.path.join(app.config['UPLOAD_FOLDER'], filename))
                product.image = filename
        
        db.session.commit()
        flash('تم تحديث المنتج بنجاح')
        return redirect(url_for('products'))
    return render_template('edit_product.html', product=product, categories=categories)

# صفحة حذف منتج
@app.route('/products/delete/<int:product_id>', methods=['POST'])
@admin_required
def delete_product(product_id):
    product = Product.query.get_or_404(product_id)
    db.session.delete(product)
    db.session.commit()
    flash('تم حذف المنتج بنجاح')
    return redirect(url_for('products'))

# صفحة الفئات
@app.route('/categories')

# صفحة إدارة العناوين
@app.route('/addresses')
@login_required
def addresses():
    # استرجاع عناوين المستخدم الحالي
    addresses = Address.query.filter_by(user_id=g.user.id).all()
    return render_template('addresses.html', addresses=addresses, current_user=g.user)

# إضافة عنوان جديد
@app.route('/add-address', methods=['POST'])
@login_required
def add_address():
    name = request.form.get('name')
    phone = request.form.get('phone')
    street = request.form.get('street')
    city = request.form.get('city')
    state = request.form.get('state')
    postal_code = request.form.get('postal_code')
    country = request.form.get('country')
    is_default = 'is_default' in request.form
    
    # إذا كان العنوان الجديد هو الافتراضي، قم بإلغاء تعيين العناوين الافتراضية الأخرى
    if is_default:
        Address.query.filter_by(user_id=g.user.id, is_default=True).update({Address.is_default: False})
    
    # إذا كان هذا أول عنوان للمستخدم، اجعله افتراضيًا
    if not Address.query.filter_by(user_id=g.user.id).first():
        is_default = True
    
    new_address = Address(
        user_id=g.user.id,
        name=name,
        phone=phone,
        street=street,
        city=city,
        state=state,
        postal_code=postal_code,
        country=country,
        is_default=is_default
    )
    
    db.session.add(new_address)
    db.session.commit()
    
    flash('تم إضافة العنوان بنجاح', 'success')
    return redirect(url_for('addresses'))

# تعديل عنوان
@app.route('/edit-address/<int:address_id>', methods=['POST'])
@login_required
def edit_address(address_id):
    address = Address.query.filter_by(id=address_id, user_id=g.user.id).first_or_404()
    
    address.name = request.form.get('name')
    address.phone = request.form.get('phone')
    address.street = request.form.get('street')
    address.city = request.form.get('city')
    address.state = request.form.get('state')
    address.postal_code = request.form.get('postal_code')
    address.country = request.form.get('country')
    
    is_default = 'is_default' in request.form
    
    # إذا كان العنوان المعدل هو الافتراضي، قم بإلغاء تعيين العناوين الافتراضية الأخرى
    if is_default and not address.is_default:
        Address.query.filter_by(user_id=g.user.id, is_default=True).update({Address.is_default: False})
        address.is_default = True
    
    db.session.commit()
    
    flash('تم تحديث العنوان بنجاح', 'success')
    return redirect(url_for('addresses'))

# حذف عنوان
@app.route('/delete-address/<int:address_id>', methods=['POST'])
@login_required
def delete_address(address_id):
    address = Address.query.filter_by(id=address_id, user_id=g.user.id).first_or_404()
    
    # إذا كان العنوان المحذوف هو الافتراضي، قم بتعيين عنوان آخر كافتراضي إذا وجد
    if address.is_default:
        next_address = Address.query.filter_by(user_id=g.user.id).filter(Address.id != address_id).first()
        if next_address:
            next_address.is_default = True
    
    db.session.delete(address)
    db.session.commit()
    
    flash('تم حذف العنوان بنجاح', 'success')
    return redirect(url_for('addresses'))

# تعيين عنوان كافتراضي
@app.route('/set-default-address/<int:address_id>', methods=['POST'])
@login_required
def set_default_address(address_id):
    # إلغاء تعيين جميع العناوين الافتراضية
    Address.query.filter_by(user_id=g.user.id, is_default=True).update({Address.is_default: False})
    
    # تعيين العنوان المحدد كافتراضي
    address = Address.query.filter_by(id=address_id, user_id=g.user.id).first_or_404()
    address.is_default = True
    
    db.session.commit()
    
    flash('تم تعيين العنوان كافتراضي بنجاح', 'success')
    return redirect(url_for('addresses'))

# صفحة الفئات
@app.route('/categories')
@admin_required
def categories():
    all_categories = Category.query.all()
    return render_template('categories.html', categories=all_categories)

# عرض منتجات فئة معينة
@app.route('/category/<int:category_id>')
def category(category_id):
    category = Category.query.get_or_404(category_id)
    products = Product.query.filter_by(category=category.name).all()
    favorite_ids = []
    if g.user:
        # الحصول على قائمة المنتجات المفضلة للمستخدم
        favorites = Favorite.query.filter_by(user_id=g.user.id).all()
        favorite_ids = [fav.product_id for fav in favorites]
    return render_template('category.html', category=category, products=products, favorite_ids=favorite_ids)

# صفحة إضافة فئة جديدة
@app.route('/categories/add', methods=['GET', 'POST'])
@admin_required
def add_category():
    if request.method == 'POST':
        name = request.form.get('name')
        description = request.form.get('description')
        
        new_category = Category(name=name, description=description)
        db.session.add(new_category)
        db.session.commit()
        flash('تم إضافة الفئة بنجاح')
        return redirect(url_for('categories'))
    return render_template('add_category.html')

# صفحة الطلبات
@app.route('/orders')
@admin_required
def orders():
    all_orders = Order.query.all()
    return render_template('orders.html', orders=all_orders)

# صفحة تفاصيل الطلب
@app.route('/orders/<int:order_id>')
@admin_required
def order_details(order_id):
    order = Order.query.get_or_404(order_id)
    return render_template('order_details.html', order=order)

# صفحة تحديث حالة الطلب
@app.route('/orders/update_status/<int:order_id>', methods=['POST'])
@admin_required
def update_order_status(order_id):
    order = Order.query.get_or_404(order_id)
    order.status = request.form.get('status')
    db.session.commit()
    flash('تم تحديث حالة الطلب بنجاح')
    return redirect(url_for('order_details', order_id=order_id))

# صفحة المستخدمين
@app.route('/users')
@admin_required
def users():
    all_users = User.query.all()
    return render_template('users.html', users=all_users, current_user=g.user)

# صفحة إضافة مستخدم جديد
@app.route('/users/add', methods=['POST'])
@admin_required
def add_user():
    if request.method == 'POST':
        username = request.form.get('username')
        email = request.form.get('email')
        password = request.form.get('password')
        is_admin = 'is_admin' in request.form
        is_active = 'is_active' in request.form
        
        # التحقق من وجود المستخدم
        existing_user = User.query.filter_by(username=username).first()
        if existing_user:
            flash('اسم المستخدم موجود بالفعل')
            return redirect(url_for('users'))
            
        # التحقق من وجود البريد الإلكتروني
        existing_email = User.query.filter_by(email=email).first()
        if existing_email:
            flash('البريد الإلكتروني موجود بالفعل')
            return redirect(url_for('users'))
        
        # إنشاء مستخدم جديد
        hashed_password = generate_password_hash(password)
        new_user = User(username=username, email=email, password=hashed_password, is_admin=is_admin, is_active=is_active)
        db.session.add(new_user)
        db.session.commit()
        flash('تم إضافة المستخدم بنجاح')
        return redirect(url_for('users'))

# صفحة تعديل مستخدم
@app.route('/users/edit/<int:user_id>', methods=['POST'])
@admin_required
def edit_user(user_id):
    user = User.query.get_or_404(user_id)
    if request.method == 'POST':
        username = request.form.get('username')
        email = request.form.get('email')

# صفحة المفضلة
@app.route('/favorites')
@login_required
def favorites():
    user_favorites = Favorite.query.filter_by(user_id=g.user.id).all()
    return render_template('favorites.html', favorites=user_favorites)

# إضافة منتج إلى المفضلة
@app.route('/add-favorite/<int:product_id>', methods=['POST'])
@login_required
def add_favorite(product_id):
    # التحقق من وجود المنتج
    product = Product.query.get_or_404(product_id)
    
    # التحقق من أن المنتج ليس في المفضلة بالفعل
    existing_favorite = Favorite.query.filter_by(user_id=g.user.id, product_id=product_id).first()
    if existing_favorite:
        flash('هذا المنتج موجود بالفعل في المفضلة', 'info')
    else:
        # إضافة المنتج إلى المفضلة
        new_favorite = Favorite(user_id=g.user.id, product_id=product_id)
        db.session.add(new_favorite)
        db.session.commit()
        flash('تمت إضافة المنتج إلى المفضلة', 'success')
    
    # العودة إلى الصفحة السابقة
    return redirect(request.referrer or url_for('index'))

# إزالة منتج من المفضلة
@app.route('/remove-favorite/<int:product_id>', methods=['POST'])
@login_required
def remove_favorite(product_id):
    # البحث عن المنتج في المفضلة
    favorite = Favorite.query.filter_by(user_id=g.user.id, product_id=product_id).first_or_404()
    
    # حذف المنتج من المفضلة
    db.session.delete(favorite)
    db.session.commit()
    flash('تمت إزالة المنتج من المفضلة', 'success')
    
    # العودة إلى الصفحة السابقة
    return redirect(request.referrer or url_for('favorites'))

# البحث عن المنتجات
@app.route('/search')
def search():
    query = request.args.get('q', '')
    if not query:
        return redirect(url_for('index'))
    
    # البحث في اسم المنتج والوصف
    products = Product.query.filter(
        db.or_(
            Product.name.ilike(f'%{query}%'),
            Product.description.ilike(f'%{query}%')
        )
    ).all()
    
    favorite_ids = []
    if g.user:
        # الحصول على قائمة المنتجات المفضلة للمستخدم
        favorites = Favorite.query.filter_by(user_id=g.user.id).all()
        favorite_ids = [fav.product_id for fav in favorites]
    
    return render_template('search.html', products=products, query=query, favorite_ids=favorite_ids)
        is_admin = 'is_admin' in request.form
        is_active = 'is_active' in request.form
        
        # التحقق من وجود المستخدم بنفس الاسم
        existing_user = User.query.filter_by(username=username).first()
        if existing_user and existing_user.id != user.id:
            flash('اسم المستخدم موجود بالفعل')
            return redirect(url_for('users'))
            
        # التحقق من وجود البريد الإلكتروني
        existing_email = User.query.filter_by(email=email).first()
        if existing_email and existing_email.id != user.id:
            flash('البريد الإلكتروني موجود بالفعل')
            return redirect(url_for('users'))
        
        user.username = username
        user.email = email
        
        # لا تسمح بتغيير صلاحيات المستخدم الحالي
        if user.id != session.get('user_id'):
            user.is_admin = is_admin
            user.is_active = is_active
        
        # تحديث كلمة المرور إذا تم إدخالها
        password = request.form.get('password')
        if password and password.strip():
            user.password = generate_password_hash(password)
        
        db.session.commit()
        flash('تم تحديث المستخدم بنجاح')
        return redirect(url_for('users'))

# صفحة حذف مستخدم
@app.route('/users/delete/<int:user_id>', methods=['POST'])
@admin_required
def delete_user(user_id):
    user = User.query.get_or_404(user_id)
    # التأكد من عدم حذف المستخدم الحالي
    if user.id == session.get('user_id'):
        flash('لا يمكنك حذف حسابك الحالي')
        return redirect(url_for('users'))
    
    db.session.delete(user)
    db.session.commit()
    flash('تم حذف المستخدم بنجاح')
    return redirect(url_for('users'))

# صفحة عرض المنتج
@app.route('/product/<int:product_id>')
def product_details(product_id):
    product = Product.query.get_or_404(product_id)
    favorite_ids = []
    if g.user:
        # الحصول على قائمة المنتجات المفضلة للمستخدم
        favorites = Favorite.query.filter_by(user_id=g.user.id).all()
        favorite_ids = [fav.product_id for fav in favorites]
    return render_template('product_details.html', product=product, favorite_ids=favorite_ids)

# إضافة منتج إلى سلة التسوق
@app.route('/cart/add/<int:product_id>', methods=['POST'])
@login_required
def add_to_cart(product_id):
    product = Product.query.get_or_404(product_id)
    quantity = int(request.form.get('quantity', 1))
    
    # التحقق من وجود المنتج في السلة
    cart_item = CartItem.query.filter_by(user_id=session['user_id'], product_id=product_id).first()
    
    if cart_item:
        cart_item.quantity += quantity
    else:
        cart_item = CartItem(user_id=session['user_id'], product_id=product_id, quantity=quantity)
        db.session.add(cart_item)
    
    db.session.commit()
    flash('تم إضافة المنتج إلى سلة التسوق')
    return redirect(url_for('cart'))

# عرض سلة التسوق
@app.route('/cart')
@login_required
def cart():
    cart_items = CartItem.query.filter_by(user_id=session['user_id']).all()
    total = sum(item.product.price * item.quantity for item in cart_items)
    return render_template('cart.html', cart_items=cart_items, total=total)

# حذف منتج من سلة التسوق
@app.route('/cart/remove/<int:item_id>', methods=['POST'])
@login_required
def remove_from_cart(item_id):
    cart_item = CartItem.query.get_or_404(item_id)
    if cart_item.user_id != session['user_id']:
        flash('غير مسموح لك بحذف هذا العنصر')
        return redirect(url_for('cart'))
    
    db.session.delete(cart_item)
    db.session.commit()
    flash('تم حذف المنتج من سلة التسوق')
    return redirect(url_for('cart'))

# إنشاء طلب جديد
@app.route('/checkout', methods=['GET', 'POST'])
@login_required
def checkout():
    cart_items = CartItem.query.filter_by(user_id=session['user_id']).all()
    if not cart_items:
        flash('سلة التسوق فارغة')
        return redirect(url_for('cart'))
    
    total = sum(item.product.price * item.quantity for item in cart_items)
    
    if request.method == 'POST':
        customer_name = request.form.get('name')
        customer_email = request.form.get('email')
        customer_phone = request.form.get('phone')
        customer_address = request.form.get('address')
        
        # إنشاء طلب جديد
        new_order = Order(
            customer_name=customer_name,
            customer_email=customer_email,
            customer_phone=customer_phone,
            customer_address=customer_address,
            total_amount=total
        )
        db.session.add(new_order)
        db.session.flush()  # للحصول على معرف الطلب
        
        # إضافة عناصر الطلب
        for item in cart_items:
            order_item = OrderItem(
                order_id=new_order.id,
                product_id=item.product_id,
                quantity=item.quantity,
                price=item.product.price
            )
            db.session.add(order_item)
            
            # تحديث المخزون
            product = item.product
            product.stock -= item.quantity
            
            # حذف العنصر من سلة التسوق
            db.session.delete(item)
        
        db.session.commit()
        flash('تم إنشاء الطلب بنجاح')
        return redirect(url_for('order_confirmation', order_id=new_order.id))
    
    return render_template('checkout.html', cart_items=cart_items, total=total)

# صفحة تأكيد الطلب
@app.route('/order/confirmation/<int:order_id>')
@login_required
def order_confirmation(order_id):
    order = Order.query.get_or_404(order_id)
    return render_template('order_confirmation.html', order=order)

# إنشاء مستخدم مسؤول إذا لم يكن موجودًا
# تم تعديل الكود لإزالة before_first_request لأنها غير متوفرة في الإصدارات الحديثة من Flask
def create_admin():
    with app.app_context():
        db.create_all()
        admin = User.query.filter_by(username='admin').first()
        if not admin:
            admin_password = generate_password_hash('admin123')
            admin = User(username='admin', password=admin_password, is_admin=True)
            db.session.add(admin)
            db.session.commit()

# استدعاء الدالة عند بدء التطبيق
with app.app_context():
    create_admin()

if __name__ == '__main__':
    with app.app_context():
        db.create_all()
    app.run(debug=True)