import React from 'react';

const Products = () => {
    const products = [
        {
            id: 1,
            name: 'Silver Ring',
            description: 'A beautiful handcrafted silver ring.',
            price: '$50',
            image: '/assets/images/silver-ring.jpg'
        },
        {
            id: 2,
            name: 'Silver Necklace',
            description: 'Elegant silver necklace with intricate design.',
            price: '$75',
            image: '/assets/images/silver-necklace.jpg'
        },
        {
            id: 3,
            name: 'Silver Bracelet',
            description: 'Stylish silver bracelet for everyday wear.',
            price: '$40',
            image: '/assets/images/silver-bracelet.jpg'
        }
    ];

    return (
        <div className="products">
            <h1>Our Silver Jewelry Collection</h1>
            <div className="product-list">
                {products.map(product => (
                    <div key={product.id} className="product-item">
                        <img src={product.image} alt={product.name} />
                        <h2>{product.name}</h2>
                        <p>{product.description}</p>
                        <p>{product.price}</p>
                    </div>
                ))}
            </div>
        </div>
    );
};

export default Products;