{% extends 'layout.html' %}

{% block title %}إعدادات الموقع{% endblock %}

{% block content %}
<div class="container py-4">
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{{ url_for('dashboard') }}">لوحة التحكم</a></li>
            <li class="breadcrumb-item active" aria-current="page">إعدادات الموقع</li>
        </ol>
    </nav>
    
    <div class="card shadow-sm mb-4">
        <div class="card-body">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-0"><i class="fas fa-cogs text-primary me-2"></i> إعدادات الموقع</h2>
                    <p class="text-muted mt-2">تخصيص إعدادات الموقع العامة</p>
                </div>
            </div>
        </div>
    </div>

    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            {% for category, message in messages %}
                <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            {% endfor %}
        {% endif %}
    {% endwith %}

    <div class="row">
        <div class="col-md-3 mb-4">
            <div class="card shadow-sm">
                <div class="card-body">
                    <div class="nav flex-column nav-pills" id="v-pills-tab" role="tablist" aria-orientation="vertical">
                        <button class="nav-link active" id="v-pills-general-tab" data-bs-toggle="pill" data-bs-target="#v-pills-general" type="button" role="tab" aria-controls="v-pills-general" aria-selected="true">
                            <i class="fas fa-sliders-h me-2"></i> إعدادات عامة
                        </button>
                        <button class="nav-link" id="v-pills-store-tab" data-bs-toggle="pill" data-bs-target="#v-pills-store" type="button" role="tab" aria-controls="v-pills-store" aria-selected="false">
                            <i class="fas fa-store me-2"></i> إعدادات المتجر
                        </button>
                        <button class="nav-link" id="v-pills-payment-tab" data-bs-toggle="pill" data-bs-target="#v-pills-payment" type="button" role="tab" aria-controls="v-pills-payment" aria-selected="false">
                            <i class="fas fa-credit-card me-2"></i> طرق الدفع
                        </button>
                        <button class="nav-link" id="v-pills-shipping-tab" data-bs-toggle="pill" data-bs-target="#v-pills-shipping" type="button" role="tab" aria-controls="v-pills-shipping" aria-selected="false">
                            <i class="fas fa-truck me-2"></i> الشحن والتوصيل
                        </button>
                        <button class="nav-link" id="v-pills-email-tab" data-bs-toggle="pill" data-bs-target="#v-pills-email" type="button" role="tab" aria-controls="v-pills-email" aria-selected="false">
                            <i class="fas fa-envelope me-2"></i> إعدادات البريد
                        </button>
                        <button class="nav-link" id="v-pills-social-tab" data-bs-toggle="pill" data-bs-target="#v-pills-social" type="button" role="tab" aria-controls="v-pills-social" aria-selected="false">
                            <i class="fas fa-share-alt me-2"></i> وسائل التواصل
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-9">
            <div class="card shadow-sm">
                <div class="card-body">
                    <div class="tab-content" id="v-pills-tabContent">
                        <!-- إعدادات عامة -->
                        <div class="tab-pane fade show active" id="v-pills-general" role="tabpanel" aria-labelledby="v-pills-general-tab">
                            <h4 class="mb-4">الإعدادات العامة</h4>
                            <form method="POST" action="{{ url_for('settings') }}" enctype="multipart/form-data">
                                <input type="hidden" name="section" value="general">
                                <div class="mb-3">
                                    <label for="site_name" class="form-label">اسم الموقع</label>
                                    <input type="text" class="form-control" id="site_name" name="site_name" value="{{ settings.site_name|default('King\'s Silverware', true) }}" required>
                                </div>
                                <div class="mb-3">
                                    <label for="site_description" class="form-label">وصف الموقع</label>
                                    <textarea class="form-control" id="site_description" name="site_description" rows="3">{{ settings.site_description|default('متجر مجوهرات فاخرة', true) }}</textarea>
                                </div>
                                <div class="mb-3">
                                    <label for="site_logo" class="form-label">شعار الموقع</label>
                                    <input type="file" class="form-control" id="site_logo" name="site_logo" accept="image/*">
                                    <small class="text-muted">اترك هذا الحقل فارغًا إذا كنت لا ترغب في تغيير الشعار الحالي</small>
                                </div>
                                <div class="mb-3">
                                    <label for="site_favicon" class="form-label">أيقونة الموقع (Favicon)</label>
                                    <input type="file" class="form-control" id="site_favicon" name="site_favicon" accept="image/x-icon,image/png">
                                    <small class="text-muted">يفضل استخدام صورة بحجم 16×16 أو 32×32 بكسل</small>
                                </div>
                                <div class="mb-3">
                                    <label for="contact_email" class="form-label">البريد الإلكتروني للتواصل</label>
                                    <input type="email" class="form-control" id="contact_email" name="contact_email" value="{{ settings.contact_email|default('<EMAIL>', true) }}">
                                </div>
                                <div class="mb-3">
                                    <label for="contact_phone" class="form-label">رقم الهاتف للتواصل</label>
                                    <input type="text" class="form-control" id="contact_phone" name="contact_phone" value="{{ settings.contact_phone|default('+966 12 345 6789', true) }}">
                                </div>
                                <div class="mb-3">
                                    <label for="address" class="form-label">عنوان المتجر</label>
                                    <textarea class="form-control" id="address" name="address" rows="3">{{ settings.address|default('شارع الملك فهد، الرياض، المملكة العربية السعودية', true) }}</textarea>
                                </div>
                                <button type="submit" class="btn btn-primary"><i class="fas fa-save me-1"></i> حفظ الإعدادات</button>
                            </form>
                        </div>
                        
                        <!-- إعدادات المتجر -->
                        <div class="tab-pane fade" id="v-pills-store" role="tabpanel" aria-labelledby="v-pills-store-tab">
                            <h4 class="mb-4">إعدادات المتجر</h4>
                            <form method="POST" action="{{ url_for('settings') }}">
                                <input type="hidden" name="section" value="store">
                                <div class="mb-3">
                                    <label for="currency" class="form-label">العملة</label>
                                    <select class="form-select" id="currency" name="currency">
                                        <option value="SAR" {% if settings.currency == 'SAR' %}selected{% endif %}>ريال سعودي (SAR)</option>
                                        <option value="USD" {% if settings.currency == 'USD' %}selected{% endif %}>دولار أمريكي (USD)</option>
                                        <option value="EUR" {% if settings.currency == 'EUR' %}selected{% endif %}>يورو (EUR)</option>
                                        <option value="AED" {% if settings.currency == 'AED' %}selected{% endif %}>درهم إماراتي (AED)</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label for="tax_rate" class="form-label">نسبة الضريبة (%)</label>
                                    <input type="number" class="form-control" id="tax_rate" name="tax_rate" value="{{ settings.tax_rate|default(15, true) }}" min="0" max="100" step="0.01">
                                </div>
                                <div class="mb-3 form-check">
                                    <input type="checkbox" class="form-check-input" id="show_out_of_stock" name="show_out_of_stock" {% if settings.show_out_of_stock %}checked{% endif %}>
                                    <label class="form-check-label" for="show_out_of_stock">عرض المنتجات غير المتوفرة في المخزون</label>
                                </div>
                                <div class="mb-3 form-check">
                                    <input type="checkbox" class="form-check-input" id="allow_reviews" name="allow_reviews" {% if settings.allow_reviews %}checked{% endif %}>
                                    <label class="form-check-label" for="allow_reviews">السماح بالتقييمات والمراجعات</label>
                                </div>
                                <div class="mb-3 form-check">
                                    <input type="checkbox" class="form-check-input" id="require_account" name="require_account" {% if settings.require_account %}checked{% endif %}>
                                    <label class="form-check-label" for="require_account">يتطلب حساب للشراء</label>
                                </div>
                                <button type="submit" class="btn btn-primary"><i class="fas fa-save me-1"></i> حفظ الإعدادات</button>
                            </form>
                        </div>
                        
                        <!-- طرق الدفع -->
                        <div class="tab-pane fade" id="v-pills-payment" role="tabpanel" aria-labelledby="v-pills-payment-tab">
                            <h4 class="mb-4">إعدادات طرق الدفع</h4>
                            <form method="POST" action="{{ url_for('settings') }}">
                                <input type="hidden" name="section" value="payment">
                                <div class="mb-3 form-check">
                                    <input type="checkbox" class="form-check-input" id="enable_cod" name="enable_cod" {% if settings.enable_cod %}checked{% endif %}>
                                    <label class="form-check-label" for="enable_cod">الدفع عند الاستلام</label>
                                </div>
                                <div class="mb-3 form-check">
                                    <input type="checkbox" class="form-check-input" id="enable_bank_transfer" name="enable_bank_transfer" {% if settings.enable_bank_transfer %}checked{% endif %}>
                                    <label class="form-check-label" for="enable_bank_transfer">التحويل البنكي</label>
                                </div>
                                <div class="mb-3 form-check">
                                    <input type="checkbox" class="form-check-input" id="enable_credit_card" name="enable_credit_card" {% if settings.enable_credit_card %}checked{% endif %}>
                                    <label class="form-check-label" for="enable_credit_card">بطاقة الائتمان</label>
                                </div>
                                <div class="mb-3">
                                    <label for="bank_details" class="form-label">تفاصيل الحساب البنكي</label>
                                    <textarea class="form-control" id="bank_details" name="bank_details" rows="3">{{ settings.bank_details|default('بنك الراجحي\nرقم الحساب: SA123456789\nاسم المستفيد: King\'s Silverware', true) }}</textarea>
                                </div>
                                <button type="submit" class="btn btn-primary"><i class="fas fa-save me-1"></i> حفظ الإعدادات</button>
                            </form>
                        </div>
                        
                        <!-- الشحن والتوصيل -->
                        <div class="tab-pane fade" id="v-pills-shipping" role="tabpanel" aria-labelledby="v-pills-shipping-tab">
                            <h4 class="mb-4">إعدادات الشحن والتوصيل</h4>
                            <form method="POST" action="{{ url_for('settings') }}">
                                <input type="hidden" name="section" value="shipping">
                                <div class="mb-3">
                                    <label for="shipping_cost" class="form-label">تكلفة الشحن الافتراضية</label>
                                    <input type="number" class="form-control" id="shipping_cost" name="shipping_cost" value="{{ settings.shipping_cost|default(30, true) }}" min="0" step="0.01">
                                </div>
                                <div class="mb-3">
                                    <label for="free_shipping_threshold" class="form-label">الحد الأدنى للشحن المجاني</label>
                                    <input type="number" class="form-control" id="free_shipping_threshold" name="free_shipping_threshold" value="{{ settings.free_shipping_threshold|default(500, true) }}" min="0" step="0.01">
                                    <small class="text-muted">اترك 0 لتعطيل الشحن المجاني</small>
                                </div>
                                <div class="mb-3 form-check">
                                    <input type="checkbox" class="form-check-input" id="enable_local_pickup" name="enable_local_pickup" {% if settings.enable_local_pickup %}checked{% endif %}>
                                    <label class="form-check-label" for="enable_local_pickup">تفعيل الاستلام من المتجر</label>
                                </div>
                                <div class="mb-3">
                                    <label for="shipping_countries" class="form-label">الدول المتاحة للشحن</label>
                                    <select class="form-select" id="shipping_countries" name="shipping_countries[]" multiple size="5">
                                        <option value="SA" selected>المملكة العربية السعودية</option>
                                        <option value="AE">الإمارات العربية المتحدة</option>
                                        <option value="KW">الكويت</option>
                                        <option value="BH">البحرين</option>
                                        <option value="QA">قطر</option>
                                        <option value="OM">عمان</option>
                                    </select>
                                    <small class="text-muted">اضغط Ctrl للاختيار المتعدد</small>
                                </div>
                                <button type="submit" class="btn btn-primary"><i class="fas fa-save me-1"></i> حفظ الإعدادات</button>
                            </form>
                        </div>
                        
                        <!-- إعدادات البريد -->
                        <div class="tab-pane fade" id="v-pills-email" role="tabpanel" aria-labelledby="v-pills-email-tab">
                            <h4 class="mb-4">إعدادات البريد الإلكتروني</h4>
                            <form method="POST" action="{{ url_for('settings') }}">
                                <input type="hidden" name="section" value="email">
                                <div class="mb-3">
                                    <label for="smtp_host" class="form-label">خادم SMTP</label>
                                    <input type="text" class="form-control" id="smtp_host" name="smtp_host" value="{{ settings.smtp_host|default('smtp.gmail.com', true) }}">
                                </div>
                                <div class="mb-3">
                                    <label for="smtp_port" class="form-label">منفذ SMTP</label>
                                    <input type="number" class="form-control" id="smtp_port" name="smtp_port" value="{{ settings.smtp_port|default(587, true) }}">
                                </div>
                                <div class="mb-3">
                                    <label for="smtp_user" class="form-label">اسم المستخدم SMTP</label>
                                    <input type="text" class="form-control" id="smtp_user" name="smtp_user" value="{{ settings.smtp_user|default('', true) }}">
                                </div>
                                <div class="mb-3">
                                    <label for="smtp_password" class="form-label">كلمة المرور SMTP</label>
                                    <input type="password" class="form-control" id="smtp_password" name="smtp_password" value="{{ settings.smtp_password|default('', true) }}">
                                </div>
                                <div class="mb-3 form-check">
                                    <input type="checkbox" class="form-check-input" id="smtp_ssl" name="smtp_ssl" {% if settings.smtp_ssl %}checked{% endif %}>
                                    <label class="form-check-label" for="smtp_ssl">استخدام SSL</label>
                                </div>
                                <div class="mb-3">
                                    <label for="email_sender" class="form-label">البريد الإلكتروني للمرسل</label>
                                    <input type="email" class="form-control" id="email_sender" name="email_sender" value="{{ settings.email_sender|default('<EMAIL>', true) }}">
                                </div>
                                <div class="mb-3">
                                    <label for="email_sender_name" class="form-label">اسم المرسل</label>
                                    <input type="text" class="form-control" id="email_sender_name" name="email_sender_name" value="{{ settings.email_sender_name|default('King\'s Silverware', true) }}">
                                </div>
                                <button type="submit" class="btn btn-primary"><i class="fas fa-save me-1"></i> حفظ الإعدادات</button>
                            </form>
                        </div>
                        
                        <!-- وسائل التواصل -->
                        <div class="tab-pane fade" id="v-pills-social" role="tabpanel" aria-labelledby="v-pills-social-tab">
                            <h4 class="mb-4">إعدادات وسائل التواصل الاجتماعي</h4>
                            <form method="POST" action="{{ url_for('settings') }}">
                                <input type="hidden" name="section" value="social">
                                <div class="mb-3">
                                    <label for="facebook" class="form-label">فيسبوك</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fab fa-facebook"></i></span>
                                        <input type="url" class="form-control" id="facebook" name="facebook" value="{{ settings.facebook|default('https://facebook.com/', true) }}">
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label for="twitter" class="form-label">تويتر</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fab fa-twitter"></i></span>
                                        <input type="url" class="form-control" id="twitter" name="twitter" value="{{ settings.twitter|default('https://twitter.com/', true) }}">
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label for="instagram" class="form-label">انستغرام</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fab fa-instagram"></i></span>
                                        <input type="url" class="form-control" id="instagram" name="instagram" value="{{ settings.instagram|default('https://instagram.com/', true) }}">
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label for="youtube" class="form-label">يوتيوب</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fab fa-youtube"></i></span>
                                        <input type="url" class="form-control" id="youtube" name="youtube" value="{{ settings.youtube|default('https://youtube.com/', true) }}">
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label for="whatsapp" class="form-label">واتساب</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fab fa-whatsapp"></i></span>
                                        <input type="text" class="form-control" id="whatsapp" name="whatsapp" value="{{ settings.whatsapp|default('+966123456789', true) }}">
                                    </div>
                                </div>
                                <button type="submit" class="btn btn-primary"><i class="fas fa-save me-1"></i> حفظ الإعدادات</button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}