from setuptools import setup, find_packages

setup(
    name="silver-jewelry-store",
    version="1.0.0",
    packages=find_packages(),
    include_package_data=True,
    install_requires=[
        'Flask',
        'Flask-Login',
        'Flask-SQLAlchemy',
        'Werkzeug',
        'Pillow',
        'email-validator',
        'WTForms',
        'Flask-WTF',
        'python-dotenv',
    ],
    author="Silver Jewelry Store",
    author_email="<EMAIL>",
    description="A Flask-based silver jewelry store application",
    keywords="flask, web, e-commerce, jewelry, silver",
    url="https://github.com/yourusername/silver-jewelry-store",
    classifiers=[
        'Development Status :: 4 - Beta',
        'Environment :: Web Environment',
        'Framework :: Flask',
        'Intended Audience :: Developers',
        'License :: OSI Approved :: MIT License',
        'Programming Language :: Python :: 3',
        'Programming Language :: Python :: 3.6',
        'Programming Language :: Python :: 3.7',
        'Programming Language :: Python :: 3.8',
        'Programming Language :: Python :: 3.9',
        'Topic :: Internet :: WWW/HTTP :: Dynamic Content',
        'Topic :: Software Development :: Libraries :: Python Modules',
    ],
    python_requires='>=3.6',
)