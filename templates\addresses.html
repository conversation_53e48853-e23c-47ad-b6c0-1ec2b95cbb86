{% extends 'layout.html' %}

{% block title %}عناوين الشحن{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-md-3 mb-4">
            <div class="card shadow-sm">
                <div class="card-body text-center py-4">
                    <div class="avatar-circle mb-3 mx-auto">
                        <span class="avatar-initials">{{ current_user.username[0] | upper }}</span>
                    </div>
                    <h5 class="card-title">{{ current_user.username }}</h5>
                    <p class="card-text text-muted">{{ current_user.email }}</p>
                    <div class="d-grid gap-2 mt-3">
                        <a href="{{ url_for('dashboard') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-tachometer-alt me-2"></i> لوحة التحكم
                        </a>
                    </div>
                </div>
                <div class="list-group list-group-flush">
                    <a href="{{ url_for('profile') }}" class="list-group-item list-group-item-action">
                        <i class="fas fa-user me-2"></i> الملف الشخصي
                    </a>
                    <a href="{{ url_for('orders') }}" class="list-group-item list-group-item-action">
                        <i class="fas fa-shopping-bag me-2"></i> طلباتي
                    </a>
                    <a href="{{ url_for('cart') }}" class="list-group-item list-group-item-action">
                        <i class="fas fa-shopping-cart me-2"></i> سلة التسوق
                    </a>
                    <a href="{{ url_for('favorites') }}" class="list-group-item list-group-item-action">
                        <i class="fas fa-heart me-2"></i> المفضلة
                    </a>
                    <a href="{{ url_for('addresses') }}" class="list-group-item list-group-item-action active">
                        <i class="fas fa-map-marker-alt me-2"></i> العناوين
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Main Content -->
        <div class="col-md-9">
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-white d-flex justify-content-between align-items-center py-3">
                    <h5 class="mb-0"><i class="fas fa-map-marker-alt me-2"></i> عناوين الشحن</h5>
                    <button type="button" class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#addAddressModal">
                        <i class="fas fa-plus-circle me-1"></i> إضافة عنوان جديد
                    </button>
                </div>
                <div class="card-body">
                    {% if addresses %}
                    <div class="row">
                        {% for address in addresses %}
                        <div class="col-md-6 mb-3">
                            <div class="card h-100 {% if address.is_default %}border-primary{% endif %}">
                                <div class="card-body">
                                    {% if address.is_default %}
                                    <div class="badge bg-primary position-absolute top-0 end-0 mt-2 me-2">العنوان الافتراضي</div>
                                    {% endif %}
                                    <h6 class="card-title">{{ address.name }}</h6>
                                    <address class="card-text mb-3">
                                        {{ address.street }}<br>
                                        {{ address.city }}, {{ address.state }} {{ address.postal_code }}<br>
                                        {{ address.country }}<br>
                                        <strong>الهاتف:</strong> {{ address.phone }}
                                    </address>
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <button type="button" class="btn btn-sm btn-outline-primary me-2" data-bs-toggle="modal" data-bs-target="#editAddress{{ address.id }}">
                                                <i class="fas fa-edit"></i> تعديل
                                            </button>
                                            <button type="button" class="btn btn-sm btn-outline-danger" data-bs-toggle="modal" data-bs-target="#deleteAddress{{ address.id }}">
                                                <i class="fas fa-trash"></i> حذف
                                            </button>
                                        </div>
                                        {% if not address.is_default %}
                                        <form method="POST" action="{{ url_for('set_default_address', address_id=address.id) }}">
                                            <button type="submit" class="btn btn-sm btn-outline-secondary">
                                                <i class="fas fa-check-circle"></i> تعيين كافتراضي
                                            </button>
                                        </form>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Edit Address Modal -->
                        <div class="modal fade" id="editAddress{{ address.id }}" tabindex="-1" aria-labelledby="editAddressLabel{{ address.id }}" aria-hidden="true">
                            <div class="modal-dialog">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h5 class="modal-title" id="editAddressLabel{{ address.id }}">تعديل العنوان</h5>
                                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                    </div>
                                    <form method="POST" action="{{ url_for('edit_address', address_id=address.id) }}">
                                        <div class="modal-body">
                                            <div class="mb-3">
                                                <label for="name{{ address.id }}" class="form-label">الاسم</label>
                                                <input type="text" class="form-control" id="name{{ address.id }}" name="name" value="{{ address.name }}" required>
                                            </div>
                                            <div class="mb-3">
                                                <label for="phone{{ address.id }}" class="form-label">رقم الهاتف</label>
                                                <input type="tel" class="form-control" id="phone{{ address.id }}" name="phone" value="{{ address.phone }}" required>
                                            </div>
                                            <div class="mb-3">
                                                <label for="street{{ address.id }}" class="form-label">الشارع</label>
                                                <input type="text" class="form-control" id="street{{ address.id }}" name="street" value="{{ address.street }}" required>
                                            </div>
                                            <div class="row">
                                                <div class="col-md-6 mb-3">
                                                    <label for="city{{ address.id }}" class="form-label">المدينة</label>
                                                    <input type="text" class="form-control" id="city{{ address.id }}" name="city" value="{{ address.city }}" required>
                                                </div>
                                                <div class="col-md-6 mb-3">
                                                    <label for="state{{ address.id }}" class="form-label">المنطقة/المحافظة</label>
                                                    <input type="text" class="form-control" id="state{{ address.id }}" name="state" value="{{ address.state }}" required>
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="col-md-6 mb-3">
                                                    <label for="postal_code{{ address.id }}" class="form-label">الرمز البريدي</label>
                                                    <input type="text" class="form-control" id="postal_code{{ address.id }}" name="postal_code" value="{{ address.postal_code }}" required>
                                                </div>
                                                <div class="col-md-6 mb-3">
                                                    <label for="country{{ address.id }}" class="form-label">الدولة</label>
                                                    <input type="text" class="form-control" id="country{{ address.id }}" name="country" value="{{ address.country }}" required>
                                                </div>
                                            </div>
                                            <div class="mb-3 form-check">
                                                <input type="checkbox" class="form-check-input" id="is_default{{ address.id }}" name="is_default" {% if address.is_default %}checked{% endif %}>
                                                <label class="form-check-label" for="is_default{{ address.id }}">تعيين كعنوان افتراضي</label>
                                            </div>
                                        </div>
                                        <div class="modal-footer">
                                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                                            <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Delete Address Modal -->
                        <div class="modal fade" id="deleteAddress{{ address.id }}" tabindex="-1" aria-labelledby="deleteAddressLabel{{ address.id }}" aria-hidden="true">
                            <div class="modal-dialog">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h5 class="modal-title" id="deleteAddressLabel{{ address.id }}">تأكيد الحذف</h5>
                                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                    </div>
                                    <div class="modal-body">
                                        <p>هل أنت متأكد من حذف هذا العنوان؟</p>
                                        {% if address.is_default %}
                                        <div class="alert alert-warning">
                                            <i class="fas fa-exclamation-triangle me-2"></i> تحذير: هذا هو العنوان الافتراضي الخاص بك. إذا قمت بحذفه، سيتعين عليك تعيين عنوان افتراضي آخر.
                                        </div>
                                        {% endif %}
                                    </div>
                                    <div class="modal-footer">
                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                                        <form method="POST" action="{{ url_for('delete_address', address_id=address.id) }}" class="d-inline">
                                            <button type="submit" class="btn btn-danger">حذف</button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-map-marker-alt fa-3x text-muted mb-3"></i>
                        <h5>لا توجد عناوين مسجلة</h5>
                        <p class="text-muted">لم تقم بإضافة أي عناوين للشحن بعد.</p>
                        <button type="button" class="btn btn-primary mt-2" data-bs-toggle="modal" data-bs-target="#addAddressModal">
                            <i class="fas fa-plus-circle me-1"></i> إضافة عنوان جديد
                        </button>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Address Modal -->
<div class="modal fade" id="addAddressModal" tabindex="-1" aria-labelledby="addAddressModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addAddressModalLabel">إضافة عنوان جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="POST" action="{{ url_for('add_address') }}">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="name" class="form-label">الاسم</label>
                        <input type="text" class="form-control" id="name" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label for="phone" class="form-label">رقم الهاتف</label>
                        <input type="tel" class="form-control" id="phone" name="phone" required>
                    </div>
                    <div class="mb-3">
                        <label for="street" class="form-label">الشارع</label>
                        <input type="text" class="form-control" id="street" name="street" required>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="city" class="form-label">المدينة</label>
                            <input type="text" class="form-control" id="city" name="city" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="state" class="form-label">المنطقة/المحافظة</label>
                            <input type="text" class="form-control" id="state" name="state" required>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="postal_code" class="form-label">الرمز البريدي</label>
                            <input type="text" class="form-control" id="postal_code" name="postal_code" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="country" class="form-label">الدولة</label>
                            <input type="text" class="form-control" id="country" name="country" required>
                        </div>
                    </div>
                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="is_default" name="is_default" {% if not addresses %}checked{% endif %}>
                        <label class="form-check-label" for="is_default">تعيين كعنوان افتراضي</label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">إضافة</button>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
.avatar-circle {
    width: 80px;
    height: 80px;
    background-color: #6c757d;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
}

.avatar-initials {
    color: white;
    font-size: 32px;
    font-weight: bold;
    text-transform: uppercase;
}
</style>
{% endblock %}