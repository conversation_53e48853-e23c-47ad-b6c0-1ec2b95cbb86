import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { FaCheck, FaCreditCard, FaPhone, FaUser, FaEnvelope, FaClipboard } from 'react-icons/fa';
import { useStudio } from '../context/StudioContext';
import { useAuth } from '../context/AuthContext';

const OrderPackage = () => {
  const { packageId } = useParams();
  const navigate = useNavigate();
  const { packages, addOrder, contactInfo } = useStudio();
  const { user } = useAuth();
  
  const [selectedPackage, setSelectedPackage] = useState(null);
  const [orderData, setOrderData] = useState({
    clientName: user?.name || '',
    clientPhone: user?.phone || '',
    clientEmail: user?.email || '',
    notes: '',
    paymentMethod: 'vodafone_cash'
  });
  const [loading, setLoading] = useState(false);
  const [orderStep, setOrderStep] = useState(1); // 1: تفاصيل الطلب, 2: الدفع, 3: تأكيد

  useEffect(() => {
    const pkg = packages.find(p => p.id === parseInt(packageId));
    if (pkg) {
      setSelectedPackage(pkg);
    } else {
      navigate('/packages');
    }
  }, [packageId, packages, navigate]);

  const handleInputChange = (e) => {
    setOrderData({
      ...orderData,
      [e.target.name]: e.target.value
    });
  };

  const validateStep1 = () => {
    return orderData.clientName && orderData.clientPhone && orderData.clientEmail;
  };

  const handleNextStep = () => {
    if (orderStep === 1 && validateStep1()) {
      setOrderStep(2);
    } else if (orderStep === 2) {
      handleSubmitOrder();
    }
  };

  const handleSubmitOrder = async () => {
    setLoading(true);
    
    try {
      const newOrder = {
        clientId: user?.id,
        clientName: orderData.clientName,
        clientPhone: orderData.clientPhone,
        clientEmail: orderData.clientEmail,
        packageId: selectedPackage.id,
        packageName: selectedPackage.name,
        amount: selectedPackage.price,
        paymentMethod: orderData.paymentMethod,
        vodafoneNumber: contactInfo.vodafoneNumber,
        notes: orderData.notes
      };

      const result = addOrder(newOrder);
      
      if (result.success) {
        setOrderStep(3);
      }
    } catch (error) {
      console.error('Error submitting order:', error);
    } finally {
      setLoading(false);
    }
  };

  const copyToClipboard = (text) => {
    navigator.clipboard.writeText(text);
    // يمكن إضافة toast notification هنا
  };

  if (!selectedPackage) {
    return <div className="loading">جاري التحميل...</div>;
  }

  return (
    <div className="order-page">
      <div className="container">
        <motion.div 
          className="order-container"
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          {/* Progress Steps */}
          <div className="order-steps">
            <div className={`step ${orderStep >= 1 ? 'active' : ''} ${orderStep > 1 ? 'completed' : ''}`}>
              <div className="step-number">1</div>
              <div className="step-label">تفاصيل الطلب</div>
            </div>
            <div className={`step ${orderStep >= 2 ? 'active' : ''} ${orderStep > 2 ? 'completed' : ''}`}>
              <div className="step-number">2</div>
              <div className="step-label">الدفع</div>
            </div>
            <div className={`step ${orderStep >= 3 ? 'active' : ''}`}>
              <div className="step-number">3</div>
              <div className="step-label">تأكيد الطلب</div>
            </div>
          </div>

          <div className="order-content">
            {/* Package Summary */}
            <div className="package-summary">
              <h3>ملخص الباقة</h3>
              <div className="summary-card">
                <div className="package-info">
                  <h4>{selectedPackage.name}</h4>
                  <p>{selectedPackage.description}</p>
                  <div className="package-price">
                    <span className="price">{selectedPackage.price}</span>
                    <span className="currency">جنيه</span>
                  </div>
                </div>
                <div className="package-features">
                  <h5>المميزات المشمولة:</h5>
                  <ul>
                    {selectedPackage.features.map((feature, index) => (
                      <li key={index}>
                        <FaCheck className="check-icon" />
                        {feature}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>

            {/* Order Form */}
            <div className="order-form-section">
              {orderStep === 1 && (
                <motion.div 
                  className="order-step"
                  initial={{ opacity: 0, x: 50 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.5 }}
                >
                  <h3>تفاصيل الطلب</h3>
                  <form className="order-form">
                    <div className="form-group">
                      <label htmlFor="clientName">
                        <FaUser className="form-icon" />
                        الاسم الكامل
                      </label>
                      <input
                        type="text"
                        id="clientName"
                        name="clientName"
                        value={orderData.clientName}
                        onChange={handleInputChange}
                        placeholder="أدخل اسمك الكامل"
                        required
                      />
                    </div>

                    <div className="form-group">
                      <label htmlFor="clientPhone">
                        <FaPhone className="form-icon" />
                        رقم الهاتف
                      </label>
                      <input
                        type="tel"
                        id="clientPhone"
                        name="clientPhone"
                        value={orderData.clientPhone}
                        onChange={handleInputChange}
                        placeholder="01xxxxxxxxx"
                        required
                      />
                    </div>

                    <div className="form-group">
                      <label htmlFor="clientEmail">
                        <FaEnvelope className="form-icon" />
                        البريد الإلكتروني
                      </label>
                      <input
                        type="email"
                        id="clientEmail"
                        name="clientEmail"
                        value={orderData.clientEmail}
                        onChange={handleInputChange}
                        placeholder="<EMAIL>"
                        required
                      />
                    </div>

                    <div className="form-group">
                      <label htmlFor="notes">
                        <FaClipboard className="form-icon" />
                        ملاحظات إضافية (اختياري)
                      </label>
                      <textarea
                        id="notes"
                        name="notes"
                        value={orderData.notes}
                        onChange={handleInputChange}
                        placeholder="أي تفاصيل إضافية تريد إضافتها للطلب..."
                        rows="4"
                      />
                    </div>
                  </form>
                </motion.div>
              )}

              {orderStep === 2 && (
                <motion.div 
                  className="order-step"
                  initial={{ opacity: 0, x: 50 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.5 }}
                >
                  <h3>طريقة الدفع</h3>
                  <div className="payment-section">
                    <div className="payment-method">
                      <div className="payment-option selected">
                        <FaCreditCard className="payment-icon" />
                        <div className="payment-info">
                          <h4>فودافون كاش</h4>
                          <p>ادفع بسهولة وأمان عبر فودافون كاش</p>
                        </div>
                      </div>
                    </div>

                    <div className="payment-instructions">
                      <h4>تعليمات الدفع:</h4>
                      <div className="instruction-card">
                        <div className="vodafone-number">
                          <span>رقم فودافون كاش:</span>
                          <div className="number-display">
                            <strong>{contactInfo.vodafoneNumber}</strong>
                            <button 
                              className="copy-btn"
                              onClick={() => copyToClipboard(contactInfo.vodafoneNumber)}
                              title="نسخ الرقم"
                            >
                              <FaClipboard />
                            </button>
                          </div>
                        </div>
                        <div className="amount-display">
                          <span>المبلغ المطلوب:</span>
                          <strong>{selectedPackage.price} جنيه</strong>
                        </div>
                      </div>
                      
                      <div className="payment-steps">
                        <h5>خطوات الدفع:</h5>
                        <ol>
                          <li>اتصل بـ *9*{contactInfo.vodafoneNumber}*{selectedPackage.price}# من هاتفك</li>
                          <li>أو استخدم تطبيق فودافون كاش</li>
                          <li>احتفظ برقم العملية للمراجعة</li>
                          <li>اضغط "تأكيد الطلب" بعد إتمام الدفع</li>
                        </ol>
                      </div>
                    </div>
                  </div>
                </motion.div>
              )}

              {orderStep === 3 && (
                <motion.div 
                  className="order-step"
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.5 }}
                >
                  <div className="order-success">
                    <div className="success-icon">
                      <FaCheck />
                    </div>
                    <h3>تم إرسال طلبك بنجاح!</h3>
                    <p>شكراً لك على ثقتك في Y-Studio</p>
                    
                    <div className="next-steps">
                      <h4>الخطوات التالية:</h4>
                      <ul>
                        <li>سيتم مراجعة طلبك خلال 24 ساعة</li>
                        <li>سنتواصل معك لتأكيد تفاصيل المشروع</li>
                        <li>سيبدأ العمل فور تأكيد الدفع</li>
                      </ul>
                    </div>

                    <div className="contact-support">
                      <p>للاستفسارات: <a href={`tel:${contactInfo.phone}`}>{contactInfo.phone}</a></p>
                    </div>

                    <div className="success-actions">
                      <button 
                        className="btn btn-primary"
                        onClick={() => navigate('/packages')}
                      >
                        تصفح باقات أخرى
                      </button>
                      <button 
                        className="btn btn-outline"
                        onClick={() => navigate('/')}
                      >
                        العودة للرئيسية
                      </button>
                    </div>
                  </div>
                </motion.div>
              )}

              {/* Action Buttons */}
              {orderStep < 3 && (
                <div className="order-actions">
                  {orderStep > 1 && (
                    <button 
                      className="btn btn-outline"
                      onClick={() => setOrderStep(orderStep - 1)}
                    >
                      السابق
                    </button>
                  )}
                  <button 
                    className="btn btn-primary"
                    onClick={handleNextStep}
                    disabled={loading || (orderStep === 1 && !validateStep1())}
                  >
                    {loading ? 'جاري المعالجة...' : 
                     orderStep === 1 ? 'التالي' : 'تأكيد الطلب'}
                  </button>
                </div>
              )}
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default OrderPackage;
