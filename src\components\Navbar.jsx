import React, { useState, useEffect } from 'react';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { useAuth } from '../context/AuthContext';
import { FaBars, FaTimes, FaUser, FaSignOutAlt, FaCog, FaHome, FaBox, FaPhone, FaWhatsapp } from 'react-icons/fa';
import { useStudio } from '../context/StudioContext';

const Navbar = () => {
  const { user, logout, isAuthenticated, isAdmin } = useAuth();
  const { contactInfo } = useStudio();
  const navigate = useNavigate();
  const location = useLocation();
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const [showUserMenu, setShowUserMenu] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const handleLogout = () => {
    logout();
    navigate('/');
    setShowUserMenu(false);
  };

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  const closeMenu = () => {
    setIsMenuOpen(false);
  };

  const navLinks = [
    { path: '/', label: 'الرئيسية', icon: <FaHome /> },
    { path: '/packages', label: 'الباقات', icon: <FaBox /> },
    { path: '/contact', label: 'اتصل بنا', icon: <FaPhone /> }
  ];

  return (
    <motion.nav 
      className={`navbar ${isScrolled ? 'scrolled' : ''}`}
      initial={{ y: -100 }}
      animate={{ y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <div className="navbar-container">
        {/* Logo */}
        <Link to="/" className="navbar-brand" onClick={closeMenu}>
          <motion.div 
            className="brand-logo"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            Y-Studio
          </motion.div>
        </Link>

        {/* Desktop Navigation */}
        <div className="navbar-menu desktop-menu">
          <ul className="navbar-nav">
            {navLinks.map((link) => (
              <li key={link.path} className="nav-item">
                <Link 
                  to={link.path} 
                  className={`nav-link ${location.pathname === link.path ? 'active' : ''}`}
                >
                  {link.icon}
                  <span>{link.label}</span>
                </Link>
              </li>
            ))}
          </ul>
        </div>

        {/* Contact & Auth Section */}
        <div className="navbar-actions">
          {/* WhatsApp Button */}
          <a 
            href={`https://wa.me/${contactInfo.phone}`}
            className="whatsapp-btn"
            target="_blank"
            rel="noopener noreferrer"
            title="تواصل عبر واتساب"
          >
            <FaWhatsapp />
          </a>

          {/* User Menu */}
          {isAuthenticated ? (
            <div className="user-menu">
              <button 
                className="user-menu-trigger"
                onClick={() => setShowUserMenu(!showUserMenu)}
              >
                <FaUser />
                <span className="user-name">{user.name}</span>
              </button>
              
              <AnimatePresence>
                {showUserMenu && (
                  <motion.div 
                    className="user-dropdown"
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    transition={{ duration: 0.2 }}
                  >
                    {isAdmin && (
                      <Link 
                        to="/admin" 
                        className="dropdown-item"
                        onClick={() => setShowUserMenu(false)}
                      >
                        <FaCog />
                        لوحة التحكم
                      </Link>
                    )}
                    <Link 
                      to="/profile" 
                      className="dropdown-item"
                      onClick={() => setShowUserMenu(false)}
                    >
                      <FaUser />
                      الملف الشخصي
                    </Link>
                    <button 
                      onClick={handleLogout}
                      className="dropdown-item logout"
                    >
                      <FaSignOutAlt />
                      تسجيل الخروج
                    </button>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          ) : (
            <div className="auth-buttons">
              <Link to="/login" className="btn btn-outline">
                تسجيل الدخول
              </Link>
              <Link to="/register" className="btn btn-primary">
                إنشاء حساب
              </Link>
            </div>
          )}

          {/* Mobile Menu Toggle */}
          <button 
            className="mobile-menu-toggle"
            onClick={toggleMenu}
            aria-label="Toggle menu"
          >
            {isMenuOpen ? <FaTimes /> : <FaBars />}
          </button>
        </div>
      </div>

      {/* Mobile Menu */}
      <AnimatePresence>
        {isMenuOpen && (
          <motion.div 
            className="mobile-menu"
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3 }}
          >
            <div className="mobile-menu-content">
              <ul className="mobile-nav">
                {navLinks.map((link) => (
                  <li key={link.path}>
                    <Link 
                      to={link.path} 
                      className={`mobile-nav-link ${location.pathname === link.path ? 'active' : ''}`}
                      onClick={closeMenu}
                    >
                      {link.icon}
                      <span>{link.label}</span>
                    </Link>
                  </li>
                ))}
              </ul>

              {isAuthenticated ? (
                <div className="mobile-user-section">
                  <div className="mobile-user-info">
                    <FaUser />
                    <span>{user.name}</span>
                  </div>
                  {isAdmin && (
                    <Link 
                      to="/admin" 
                      className="mobile-nav-link"
                      onClick={closeMenu}
                    >
                      <FaCog />
                      <span>لوحة التحكم</span>
                    </Link>
                  )}
                  <Link 
                    to="/profile" 
                    className="mobile-nav-link"
                    onClick={closeMenu}
                  >
                    <FaUser />
                    <span>الملف الشخصي</span>
                  </Link>
                  <button 
                    onClick={handleLogout}
                    className="mobile-nav-link logout"
                  >
                    <FaSignOutAlt />
                    <span>تسجيل الخروج</span>
                  </button>
                </div>
              ) : (
                <div className="mobile-auth-section">
                  <Link 
                    to="/login" 
                    className="btn btn-outline mobile-auth-btn"
                    onClick={closeMenu}
                  >
                    تسجيل الدخول
                  </Link>
                  <Link 
                    to="/register" 
                    className="btn btn-primary mobile-auth-btn"
                    onClick={closeMenu}
                  >
                    إنشاء حساب
                  </Link>
                </div>
              )}

              <div className="mobile-contact">
                <a 
                  href={`https://wa.me/${contactInfo.phone}`}
                  className="mobile-whatsapp"
                  target="_blank"
                  rel="noopener noreferrer"
                  onClick={closeMenu}
                >
                  <FaWhatsapp />
                  <span>تواصل عبر واتساب</span>
                </a>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Overlay */}
      {isMenuOpen && (
        <div 
          className="mobile-menu-overlay"
          onClick={closeMenu}
        />
      )}
    </motion.nav>
  );
};

export default Navbar;
