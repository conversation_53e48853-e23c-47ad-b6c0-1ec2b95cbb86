import React, { createContext, useContext, useState, useEffect } from 'react';

const AuthContext = createContext();

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);

  // محاكاة قاعدة بيانات المستخدمين
  const users = [
    {
      id: 1,
      username: 'admin',
      password: 'admin123',
      role: 'admin',
      name: 'مدير Y-Studio',
      email: '<EMAIL>',
      phone: '01211157539'
    },
    {
      id: 2,
      username: 'client1',
      password: 'client123',
      role: 'client',
      name: 'عميل تجريبي',
      email: '<EMAIL>',
      phone: '01234567890'
    }
  ];

  useEffect(() => {
    // فحص إذا كان المستخدم مسجل دخول مسبقاً
    const savedUser = localStorage.getItem('y_studio_user');
    if (savedUser) {
      setUser(JSON.parse(savedUser));
    }
    setLoading(false);
  }, []);

  const login = (username, password) => {
    const foundUser = users.find(
      u => u.username === username && u.password === password
    );
    
    if (foundUser) {
      const userInfo = {
        id: foundUser.id,
        username: foundUser.username,
        role: foundUser.role,
        name: foundUser.name,
        email: foundUser.email,
        phone: foundUser.phone
      };
      setUser(userInfo);
      localStorage.setItem('y_studio_user', JSON.stringify(userInfo));
      return { success: true, user: userInfo };
    }
    
    return { success: false, message: 'اسم المستخدم أو كلمة المرور غير صحيحة' };
  };

  const register = (userData) => {
    // فحص إذا كان اسم المستخدم موجود
    const existingUser = users.find(u => u.username === userData.username);
    if (existingUser) {
      return { success: false, message: 'اسم المستخدم موجود بالفعل' };
    }

    // فحص إذا كان البريد الإلكتروني موجود
    const existingEmail = users.find(u => u.email === userData.email);
    if (existingEmail) {
      return { success: false, message: 'البريد الإلكتروني مستخدم بالفعل' };
    }

    // إضافة مستخدم جديد
    const newUser = {
      id: users.length + 1,
      username: userData.username,
      password: userData.password,
      role: 'client',
      name: userData.name,
      email: userData.email,
      phone: userData.phone
    };
    
    users.push(newUser);
    
    const userInfo = {
      id: newUser.id,
      username: newUser.username,
      role: newUser.role,
      name: newUser.name,
      email: newUser.email,
      phone: newUser.phone
    };
    
    setUser(userInfo);
    localStorage.setItem('y_studio_user', JSON.stringify(userInfo));
    return { success: true, user: userInfo };
  };

  const logout = () => {
    setUser(null);
    localStorage.removeItem('y_studio_user');
  };

  const updateProfile = (updatedData) => {
    const updatedUser = { ...user, ...updatedData };
    setUser(updatedUser);
    localStorage.setItem('y_studio_user', JSON.stringify(updatedUser));
    return { success: true, user: updatedUser };
  };

  const value = {
    user,
    login,
    register,
    logout,
    updateProfile,
    loading,
    isAdmin: user?.role === 'admin',
    isClient: user?.role === 'client',
    isAuthenticated: !!user
  };

  return (
    <AuthContext.Provider value={value}>
      {!loading && children}
    </AuthContext.Provider>
  );
};
