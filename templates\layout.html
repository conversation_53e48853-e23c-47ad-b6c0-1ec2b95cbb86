<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فضيات الملك - {% block title %}الصفحة الرئيسية{% endblock %}</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
</head>
<body>
    <!-- شريط التنقل -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('home') }}">فضيات الملك</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto mb-2 mb-lg-0">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('home') }}">الرئيسية</a>
                    </li>
                    {% if session.get('user_id') %}
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('dashboard') }}">لوحة التحكم</a>
                        </li>
                        {% if session.get('is_admin') %}
                            <li class="nav-item">
                                <a class="nav-link" href="{{ url_for('products') }}">المنتجات</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="{{ url_for('categories') }}">الفئات</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="{{ url_for('orders') }}">الطلبات</a>
                            </li>
                        {% endif %}
                    {% endif %}
                </ul>
                <ul class="navbar-nav">
                    {% if session.get('user_id') %}
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('cart') }}">
                                <i class="fas fa-shopping-cart"></i> سلة التسوق
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('logout') }}">تسجيل الخروج</a>
                        </li>
                    {% else %}
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('login') }}">تسجيل الدخول</a>
                        </li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </nav>

    <!-- رسائل التنبيه -->
    <div class="container mt-3">
        {% with messages = get_flashed_messages() %}
            {% if messages %}
                {% for message in messages %}
                    <div class="alert alert-info alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}
    </div>

    <!-- المحتوى الرئيسي -->
    <main class="container my-4">
        {% block content %}{% endblock %}
    </main>

    <!-- تذييل الصفحة -->
    <footer class="bg-dark text-white py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-4">
                    <h5>فضيات الملك</h5>
                    <p>أفضل متجر للمجوهرات الفضية عالية الجودة</p>
                </div>
                <div class="col-md-4">
                    <h5>روابط سريعة</h5>
                    <ul class="list-unstyled">
                        <li><a href="{{ url_for('home') }}" class="text-white">الرئيسية</a></li>
                        <li><a href="#" class="text-white">من نحن</a></li>
                        <li><a href="#" class="text-white">اتصل بنا</a></li>
                    </ul>
                </div>
                <div class="col-md-4">
                    <h5>تواصل معنا</h5>
                    <ul class="list-unstyled">
                        <li><i class="fas fa-envelope me-2"></i> <EMAIL></li>
                        <li><i class="fas fa-phone me-2"></i> +************</li>
                        <li><i class="fas fa-map-marker-alt me-2"></i> الرياض، المملكة العربية السعودية</li>
                    </ul>
                </div>
            </div>
            <hr>
            <div class="text-center">
                <p>جميع الحقوق محفوظة &copy; {{ now.year }} فضيات الملك</p>
            </div>
        </div>
    </footer>

    <!-- سكربتات جافاسكربت -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
    {% block scripts %}{% endblock %}
</body>
</html>