import React from 'react';
import { Navigate } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';

const ProtectedRoute = ({ children, adminOnly = false, clientOnly = false }) => {
  const { user, isAuthenticated } = useAuth();

  // إذا لم يكن المستخدم مسجل دخول
  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }

  // إذا كانت الصفحة للإدارة فقط والمستخدم ليس إدارياً
  if (adminOnly && user.role !== 'admin') {
    return <Navigate to="/" replace />;
  }

  // إذا كانت الصفحة للعملاء فقط والمستخدم ليس عميلاً
  if (clientOnly && user.role !== 'client') {
    return <Navigate to="/admin" replace />;
  }

  return children;
};

export default ProtectedRoute;
